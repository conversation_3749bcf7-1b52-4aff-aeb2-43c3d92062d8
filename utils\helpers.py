# utils/helpers.py
from typing import Dict, List, Tuple, Generator
import json
from datetime import datetime

def batch_list(items: List, batch_size: int) -> Generator[List, None, None]:
    """Split a list into batches"""
    for i in range(0, len(items), batch_size):
        yield items[i:i + batch_size]

def calculate_opportunity_score(search_volume: int, difficulty: float, cpc: float = 0) -> float:
    """Calculate an opportunity score for a keyword"""
    # Higher volume is better
    volume_score = min(search_volume / 1000, 10)
    
    # Lower difficulty is better
    difficulty_score = 10 - (difficulty / 10)
    
    # Higher CPC indicates commercial value
    cpc_score = min(cpc, 10)
    
    # Weighted average
    return (volume_score * 0.4 + difficulty_score * 0.4 + cpc_score * 0.2)

def format_cluster_summary(cluster: Dict) -> str:
    """Format a cluster for display"""
    subtopics = json.loads(cluster.get('subtopics', '[]'))
    
    return f"""
    Pillar: {cluster.get('pillar_keyword', 'Unknown')}
    Subtopics: {len(subtopics)}
    Total SV: {cluster.get('total_search_volume', 0):,}
    Avg KD: {cluster.get('avg_keyword_difficulty', 0):.1f}
    Status: {'✓ Brief Generated' if cluster.get('brief') else '⏳ Pending'}
    """

def export_keywords_csv(keywords: List[Dict]) -> str:
    """Export keywords to CSV format"""
    headers = ['Keyword', 'Search Volume', 'Keyword Difficulty', 'CPC', 'Intent', 'Funnel Stage']
    
    rows = [','.join(headers)]
    for kw in keywords:
        row = [
            kw.get('keyword', ''),
            str(kw.get('search_volume', 0)),
            str(kw.get('keyword_difficulty', 0)),
            str(kw.get('cpc', 0)),
            kw.get('search_intent', ''),
            kw.get('funnel_stage', '')
        ]
        rows.append(','.join(row))
    
    return '\n'.join(rows)
