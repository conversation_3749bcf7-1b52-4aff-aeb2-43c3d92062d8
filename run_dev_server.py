# run_dev_server.py
import sys
import os

# Add the project root to Python path FIRST
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# Import and run the development server
from scripts.dev_server import mcp

if __name__ == "__main__":
    print("Starting SEO Pipeline Development Server...")
    print("This server includes tools for testing all services")
    print("Use MCP Inspector to connect and test")
    mcp.run()
