# models/keyword.py
from pydantic import BaseModel, <PERSON>
from typing import Optional
from datetime import datetime

class Keyword(BaseModel):
    id: Optional[int] = None
    raw_keyword: str
    normalized_keyword: Optional[str] = None
    keyword: str
    search_volume: int = 0
    keyword_difficulty: float = 0
    cpc: float = 0
    intent: Optional[str] = None
    search_intent: Optional[str] = None
    funnel_stage: Optional[str] = None
    cluster_id: Optional[int] = None
    status: str = "New"
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: Optional[datetime] = None

