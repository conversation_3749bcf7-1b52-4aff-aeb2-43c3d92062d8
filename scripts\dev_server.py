# scripts/dev_server.py
#!/usr/bin/env python
"""
Development server for testing the SEO pipeline
"""
import asyncio
from pathlib import Path
import sys

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from mcp.server.fastmcp import FastMCP
from services.nocodb_service import NocoDBService
from services.dataforseo_service import DataForSeoService
from services.openai_service import OpenAIService
from config.settings import settings

# Create a combined development server
mcp = FastMCP("SEO Pipeline Dev Server")

# Initialize services
nocodb = NocoDBService()
dataforseo = DataForSeoService(settings)
openai = OpenAIService()

@mcp.tool(description="Test NocoDB connection")
async def test_nocodb() -> dict:
    """Test the NocoDB connection"""
    try:
        # Try to fetch from a test table
        result = await nocodb.get_table_data("seed_keywords")
        return {"status": "success", "message": "NocoDB connected", "records": len(result)}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool(description="Test DataForSEO connection")
async def test_dataforseo() -> dict:
    """Test the DataForSEO connection"""
    try:
        # Try a simple keyword lookup
        result = await dataforseo.get_keyword_metrics(["seo tools"])
        return {"status": "success", "message": "DataForSEO connected", "data": result}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool(description="Test OpenAI connection")
async def test_openai() -> dict:
    """Test the OpenAI connection"""
    try:
        result = await openai.normalize_keyword("test keyword")
        return {"status": "success", "message": "OpenAI connected", "normalized": result}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool(description="Run full pipeline test")
async def test_full_pipeline() -> dict:
    """Test the full SEO pipeline with sample data"""
    results = {}
    
    # Test each service
    results["nocodb"] = await test_nocodb()
    results["dataforseo"] = await test_dataforseo()
    results["openai"] = await test_openai()
    
    # Check if all services are working
    all_working = all(r["status"] == "success" for r in results.values())
    
    return {
        "all_services_working": all_working,
        "service_results": results,
        "recommendation": "Ready to run pipeline" if all_working else "Fix service connections first"
    }

@mcp.resource("pipeline://status")
async def get_pipeline_status() -> str:
    """Get the current status of the SEO pipeline"""
    return """
    SEO Pipeline Status
    ==================
    
    This is a development server for testing the SEO pipeline.
    
    Available test commands:
    - test_nocodb: Test database connection
    - test_dataforseo: Test SEO data API
    - test_openai: Test AI service
    - test_full_pipeline: Run all tests
    
    Use 'mcp dev scripts/dev_server.py' to run this server.
    """

if __name__ == "__main__":
    print("Starting SEO Pipeline Development Server...")
    print("Use 'mcp dev scripts/dev_server.py' to test")
    mcp.run()