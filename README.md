# MCP SEO Pipeline

A comprehensive **Model Context Protocol (MCP)** based SEO automation pipeline that integrates NocoDB, DataForSEO, and OpenAI to streamline keyword research, content planning, and SEO analysis.

## 🎯 Overview

This pipeline automates the entire SEO workflow from seed keywords to content briefs:

1. **Seed Keywords** → Normalize and classify intent
2. **Keyword Expansion** → Get search volume and difficulty data
3. **Intent Classification** → Categorize by search intent and funnel stage
4. **Cluster Building** → Group related keywords into content topics
5. **Content Briefs** → Generate AI-powered content outlines
6. **Competitor Analysis** → Analyze top-ranking content
7. **Performance Tracking** → Monitor keyword rankings over time

## 🏗️ Architecture

### MCP Servers (8 specialized servers)
- `seed_keyword_server.py` - Process and normalize initial keywords
- `keyword_expansion_server.py` - Expand keywords using DataForSEO
- `intent_classifier_server.py` - Classify search intent using AI
- `cluster_builder_server.py` - Group related keywords into clusters
- `content_brief_server.py` - Generate AI-powered content briefs
- `competitor_snapshot_server.py` - Analyze competitor content
- `performance_tracker_server.py` - Track keyword rankings
- `notification_server.py` - Send alerts and updates

### Services Integration
- **NocoDB** - Database for storing all SEO data
- **DataForSEO** - Real-time keyword metrics and SERP data
- **OpenAI** - AI-powered content analysis and generation
- **Notifications** - Slack/Email alerts

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- NocoDB instance running (http://localhost:8080)
- API credentials for DataForSEO and OpenAI

### Installation

1. **Clone and setup environment:**
```bash
git clone <your-repo-url>
cd mcp-seo-pipeline
python -m venv myenv
myenv\Scripts\activate  # Windows
# or
source myenv/bin/activate  # Linux/Mac
pip install -r requirements.txt
```

2. **Configure environment variables:**
```bash
cp .env.example .env
# Edit .env with your actual API credentials
```

3. **Setup NocoDB database:**
```bash
# Run the database setup script
python scripts/setup_database.py

# Then manually create tables in NocoDB UI at http://localhost:8080
# Use the table schemas provided in the script output
```

4. **Test the development server:**
```bash
python scripts/dev_server.py
```

5. **Install MCP servers:**
```bash
python scripts/install_servers.py
```

## 📊 Database Schema

### Tables Required in NocoDB:

#### seed_keywords
- `id` (Primary Key)
- `raw_keyword` (Text)
- `normalized_keyword` (Text)
- `intent` (Text)
- `status` (Text, default: 'New')
- `created_at` (DateTime)
- `updated_at` (DateTime)

#### expanded_keywords
- `id` (Primary Key)
- `seed_keyword_id` (Integer, Foreign Key)
- `keyword` (Text)
- `search_volume` (Integer)
- `keyword_difficulty` (Float)
- `cpc` (Float)
- `search_intent` (Text)
- `funnel_stage` (Text)
- `cluster_id` (Integer)
- `created_at` (DateTime)

#### clusters
- `id` (Primary Key)
- `pillar_keyword` (Text)
- `subtopics` (Text)
- `total_search_volume` (Integer)
- `avg_keyword_difficulty` (Float)
- `approved_for_brief` (Boolean, default: False)
- `brief` (Text)
- `brief_generated_at` (DateTime)
- `created_at` (DateTime)
- `updated_at` (DateTime)

#### competitor_audit
- `id` (Primary Key)
- `keyword` (Text)
- `competitor_url` (Text)
- `title` (Text)
- `word_count` (Integer)
- `position` (Integer)
- `scraped_at` (DateTime)

#### performance_tracking
- `id` (Primary Key)
- `keyword` (Text)
- `baseline_position` (Integer)
- `current_position` (Integer)
- `position_change` (Integer)
- `tracked_at` (DateTime)

## 🔧 Configuration

### Environment Variables (.env)
```env
# NocoDB
NOCODB_API_URL=http://localhost:8080
NOCODB_API_TOKEN=your_nocodb_api_token

# DataForSEO
DATAFORSEO_LOGIN=your_login
DATAFORSEO_PASSWORD=your_password

# OpenAI
OPENAI_API_KEY=your_openai_api_key

# Notifications
SLACK_WEBHOOK_URL=your_slack_webhook_url
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
```

## 🎮 Usage

### Option A: Through Claude Desktop (Recommended)
1. Install the MCP servers: `python scripts/install_servers.py`
2. Open Claude Desktop
3. The installed servers will appear in the tools menu
4. Use them sequentially or as needed

### Option B: Manual Execution
```bash
# 1. Add seed keywords to NocoDB manually
# 2. Process seed keywords
python -m mcp_servers.seed_keyword_server

# 3. Expand keywords
python -m mcp_servers.keyword_expansion_server

# 4. Classify intent
python -m mcp_servers.intent_classifier_server

# 5. Build clusters
python -m mcp_servers.cluster_builder_server

# 6. Generate briefs (after approving clusters in NocoDB)
python -m mcp_servers.content_brief_server

# 7. Run competitor analysis
python -m mcp_servers.competitor_snapshot_server

# 8. Track performance
python -m mcp_servers.performance_tracker_server
```

### Option C: Development Testing
```bash
# Test the development server
python scripts/dev_server.py

# Or use MCP CLI
mcp dev scripts/dev_server.py

# Test individual servers
mcp dev mcp_servers/seed_keyword_server.py
```

## 🔍 Testing

### Test Individual Components
```bash
# Test database connection
python -c "from services.nocodb_service import NocoDBService; import asyncio; asyncio.run(NocoDBService().get_table_data('seed_keywords'))"

# Test DataForSEO connection
python -c "from services.dataforseo_service import DataForSEOService; import asyncio; asyncio.run(DataForSEOService().get_keyword_metrics(['seo tools']))"

# Test OpenAI connection
python -c "from services.openai_service import OpenAIService; import asyncio; asyncio.run(OpenAIService().normalize_keyword('test keyword'))"
```

### Full Pipeline Test
```bash
# Use the development server's test tools
python scripts/dev_server.py
# Then use the test_full_pipeline tool
```

## 🤖 Automation Setup (Optional)

### Cron Jobs for Daily Tasks
```bash
crontab -e

# Add these lines:
# Daily keyword processing at 2 AM
0 2 * * * cd /path/to/mcp-seo-pipeline && myenv/Scripts/python -m mcp_servers.cluster_builder_server

# Weekly competitor analysis on Sundays
0 3 * * 0 cd /path/to/mcp-seo-pipeline && myenv/Scripts/python -m mcp_servers.competitor_snapshot_server

# Quarterly performance review (1st of Jan, Apr, Jul, Oct)
0 4 1 1,4,7,10 * cd /path/to/mcp-seo-pipeline && myenv/Scripts/python -m mcp_servers.performance_tracker_server
```

## 🛠️ Troubleshooting

### Common Issues
```bash
# Check if all services are working
mcp dev scripts/dev_server.py
# Then use the test_full_pipeline tool

# View MCP logs
tail -f ~/.mcp/logs/*.log

# List installed MCP servers
mcp list

# Uninstall a server
mcp uninstall seo-seed-keyword-server

# Check Python path issues
python -c "import sys; print('\n'.join(sys.path))"

# Verify environment variables
python -c "from config.settings import settings; print(settings)"
```

### Pydantic Import Error
If you get a `BaseSettings` import error, make sure you're using the correct import:
```python
from pydantic_settings import BaseSettings  # Correct
# not: from pydantic import BaseSettings  # Old version
```

## 📁 Project Structure
```
mcp-seo-pipeline/
├── config/
│   ├── __init__.py
│   └── settings.py
├── mcp_servers/
│   ├── __init__.py
│   ├── seed_keyword_server.py
│   ├── keyword_expansion_server.py
│   ├── intent_classifier_server.py
│   ├── cluster_builder_server.py
│   ├── content_brief_server.py
│   ├── competitor_snapshot_server.py
│   ├── performance_tracker_server.py
│   └── notification_server.py
├── services/
│   ├── __init__.py
│   ├── nocodb_service.py
│   ├── dataforseo_service.py
│   ├── openai_service.py
│   └── notification_service.py
├── models/
│   ├── __init__.py
│   ├── keyword.py
│   ├── cluster.py
│   └── brief.py
├── utils/
│   ├── __init__.py
│   ├── helpers.py
│   └── validators.py
├── scripts/
│   ├── install_servers.py
│   ├── dev_server.py
│   └── setup_database.py
├── myenv/                 # Virtual environment
├── .env                   # Environment variables
├── .env.example          # Environment template
├── requirements.txt      # Python dependencies
├── pyproject.toml       # Project configuration
└── README.md            # This file
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For issues and questions:
1. Check the troubleshooting section above
2. Review the logs in `~/.mcp/logs/`
3. Open an issue on GitHub

---

**Happy SEO automating! 🚀**
