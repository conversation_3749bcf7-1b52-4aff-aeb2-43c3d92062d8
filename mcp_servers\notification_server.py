# mcp_servers/notification_server.py
from mcp.server.fastmcp import FastMCP, Context
from services.sqlite_services import SQLiteService
from services.notification_service import NotificationService
from typing import Dict, List, Optional
from datetime import datetime, timedelta
import json

mcp = FastMCP("SEO Notification Hub")
nocodb = SQLiteService()
notifications = NotificationService()

@mcp.tool(description="Send daily SEO summary")
async def send_daily_summary(ctx: Context) -> Dict:
    """
    Send a daily summary of SEO pipeline activities
    """
    ctx.info("Generating daily SEO summary")
    
    # Get today's data
    today = datetime.now().date()
    
    # Fetch recent activities
    seed_keywords = await nocodb.get_table_data("seed_keywords")
    expanded_keywords = await nocodb.get_table_data("expanded_keywords")
    clusters = await nocodb.get_table_data("clusters")
    
    # Calculate daily metrics
    new_seeds_today = len([k for k in seed_keywords 
                          if k.get("created_at") and 
                          datetime.fromisoformat(k["created_at"]).date() == today])
    
    new_expanded_today = len([k for k in expanded_keywords 
                             if k.get("created_at") and 
                             datetime.fromisoformat(k["created_at"]).date() == today])
    
    briefs_pending = len([c for c in clusters 
                         if c.get("approved_for_brief") and not c.get("brief")])
    
    # Build summary message
    summary = f"""
    📊 Daily SEO Pipeline Summary - {today}
    
    🌱 New Seed Keywords: {new_seeds_today}
    🔍 Keywords Expanded Today: {new_expanded_today}
    📝 Briefs Pending Generation: {briefs_pending}
    
    Total Keywords in Database: {len(expanded_keywords)}
    Total Clusters: {len(clusters)}
    
    Top Opportunity:
    """
    
    # Add top opportunity
    if clusters:
        top_cluster = max(clusters, 
                         key=lambda c: c.get("total_search_volume", 0) / 
                                     (c.get("avg_keyword_difficulty", 100) + 1))
        summary += f"- {top_cluster['pillar_keyword']} (SV: {top_cluster['total_search_volume']:,})"
    
    # Send notifications
    if notifications.slack_webhook_url:
        await notifications.send_slack_message(summary)
    
    return {
        "summary_sent": True,
        "metrics": {
            "new_seeds": new_seeds_today,
            "new_expanded": new_expanded_today,
            "briefs_pending": briefs_pending
        }
    }

@mcp.tool(description="Send weekly performance report")
async def send_weekly_report(ctx: Context) -> Dict:
    """
    Send a comprehensive weekly SEO performance report
    """
    ctx.info("Generating weekly SEO report")
    
    # Calculate date range
    end_date = datetime.now()
    start_date = end_date - timedelta(days=7)
    
    # Gather weekly data
    all_keywords = await nocodb.get_table_data("expanded_keywords")
    clusters = await nocodb.get_table_data("clusters")
    competitor_data = await nocodb.get_table_data("competitor_audit")
    
    # Filter for this week's data
    week_keywords = [k for k in all_keywords 
                    if k.get("created_at") and 
                    start_date <= datetime.fromisoformat(k["created_at"]) <= end_date]
    
    week_clusters = [c for c in clusters 
                    if c.get("created_at") and 
                    start_date <= datetime.fromisoformat(c["created_at"]) <= end_date]
    
    # Calculate metrics
    total_search_volume = sum(k.get("search_volume", 0) for k in week_keywords)
    avg_difficulty = sum(k.get("keyword_difficulty", 0) for k in week_keywords) / len(week_keywords) if week_keywords else 0
    
    # Intent breakdown
    intent_counts = {}
    for kw in week_keywords:
        intent = kw.get("search_intent", "unknown")
        intent_counts[intent] = intent_counts.get(intent, 0) + 1
    
    report = f"""
    📈 Weekly SEO Performance Report
    Period: {start_date.date()} to {end_date.date()}
    
    🎯 Keywords Discovered: {len(week_keywords)}
    📊 Total Search Volume: {total_search_volume:,}
    📉 Average Difficulty: {avg_difficulty:.1f}
    
    🏗️ New Clusters Created: {len(week_clusters)}
    
    Search Intent Breakdown:
    """
    
    for intent, count in intent_counts.items():
        percentage = (count / len(week_keywords) * 100) if week_keywords else 0
        report += f"\n- {intent.capitalize()}: {count} ({percentage:.1f}%)"
    
    # Add top performing cluster
    if week_clusters:
        top_cluster = max(week_clusters, key=lambda c: c.get("total_search_volume", 0))
        report += f"\n\n🌟 Top New Cluster:\n{top_cluster['pillar_keyword']} - {top_cluster['total_search_volume']:,} monthly searches"
    
    # Send report
    await notifications.send_email(
        to=["<EMAIL>", "<EMAIL>"],
        subject="Weekly SEO Pipeline Report",
        body=report
    )
    
    if notifications.slack_webhook_url:
        await notifications.send_slack_message(report)
    
    return {
        "report_sent": True,
        "week_metrics": {
            "keywords_discovered": len(week_keywords),
            "clusters_created": len(week_clusters),
            "total_search_volume": total_search_volume
        }
    }

@mcp.tool(description="Alert on high-value opportunities")
async def alert_high_value_opportunities(min_search_volume: int = 10000, max_difficulty: float = 40) -> Dict:
    """
    Alert team about high-value keyword opportunities
    """
    # Find high-value clusters
    clusters = await nocodb.get_table_data("clusters")
    
    opportunities = []
    for cluster in clusters:
        if (cluster.get("total_search_volume", 0) >= min_search_volume and 
            cluster.get("avg_keyword_difficulty", 100) <= max_difficulty and
            not cluster.get("brief")):
            opportunities.append(cluster)
    
    if opportunities:
        alert_message = f"""
        🚨 High-Value SEO Opportunities Detected!
        
        Found {len(opportunities)} clusters with:
        - Search Volume ≥ {min_search_volume:,}
        - Keyword Difficulty ≤ {max_difficulty}
        - No content brief yet
        
        Top Opportunities:
        """
        
        # Sort by opportunity score
        opportunities.sort(key=lambda c: c.get("total_search_volume", 0) / 
                                       (c.get("avg_keyword_difficulty", 1) + 1), 
                          reverse=True)
        
        for opp in opportunities[:5]:
            alert_message += f"\n• {opp['pillar_keyword']} - SV: {opp['total_search_volume']:,}, KD: {opp['avg_keyword_difficulty']:.1f}"
        
        # Send alert
        await notifications.send_slack_message(alert_message)
        
        return {
            "alerts_sent": True,
            "opportunities_found": len(opportunities),
            "top_opportunities": [o["pillar_keyword"] for o in opportunities[:5]]
        }
    
    return {
        "alerts_sent": False,
        "opportunities_found": 0,
        "message": "No high-value opportunities matching criteria"
    }

@mcp.tool(description="Send content brief notification")
async def notify_content_brief_ready(cluster_id: str, recipients: List[str]) -> Dict:
    """
    Notify writers when a content brief is ready
    """
    # Get cluster and brief data
    clusters = await nocodb.get_table_data("clusters", {"id": cluster_id})
    if not clusters or not clusters[0].get("brief"):
        return {"error": "Cluster not found or brief not generated"}
    
    cluster = clusters[0]
    subtopics = json.loads(cluster.get("subtopics", "[]"))
    
    # Create notification
    message = f"""
    ✍️ New Content Brief Ready!
    
    Topic: {cluster['pillar_keyword']}
    Search Volume: {cluster['total_search_volume']:,} monthly searches
    Difficulty: {cluster['avg_keyword_difficulty']:.1f}/100
    
    Subtopics to Cover ({len(subtopics)}):
    {', '.join(subtopics[:5])}{'...' if len(subtopics) > 5 else ''}
    
    Brief Preview:
    {cluster['brief'][:300]}...
    
    Full brief available in NocoDB: {nocodb.base_url}/dashboard/clusters/{cluster_id}
    """
    
    # Send to Slack
    if notifications.slack_webhook_url:
        await notifications.send_slack_message(message)
    
    # Send email to specific recipients
    if recipients:
        await notifications.send_email(
            to=recipients,
            subject=f"Content Brief Ready: {cluster['pillar_keyword']}",
            body=message + f"\n\nFull Brief:\n\n{cluster['brief']}"
        )
    
    return {
        "notifications_sent": True,
        "cluster": cluster['pillar_keyword'],
        "recipients": recipients
    }

@mcp.tool(description="Send ranking drop alerts")
async def alert_ranking_drops(threshold: int = 3) -> Dict:
    """
    Alert on significant ranking drops
    """
    # Get recent performance tracking data
    tracking_data = await nocodb.get_table_data("performance_tracking")
    
    # Find significant drops
    drops = []
    for track in tracking_data:
        if track.get("position_change", 0) > threshold:
            drops.append({
                "keyword": track["keyword"],
                "previous": track.get("baseline_position", 0),
                "current": track.get("current_position", 0),
                "change": track.get("position_change", 0)
            })
    
    if drops:
        # Sort by biggest drops
        drops.sort(key=lambda x: x["change"], reverse=True)
        
        alert = f"""
        📉 SEO Alert: Significant Ranking Drops Detected
        
        {len(drops)} keywords dropped more than {threshold} positions:
        """
        
        for drop in drops[:10]:  # Top 10 drops
            alert += f"\n• {drop['keyword']}: #{drop['previous']} → #{drop['current']} (↓{drop['change']})"
        
        if len(drops) > 10:
            alert += f"\n\n...and {len(drops) - 10} more"
        
        alert += "\n\n⚡ Recommended Actions:\n1. Review recent algorithm updates\n2. Check competitor changes\n3. Update affected content"
        
        # Send urgent alert
        await notifications.send_slack_message(alert)
        
        # Email for serious drops
        if any(d["change"] > 10 for d in drops):
            await notifications.send_email(
                to=["<EMAIL>"],
                subject="URGENT: Major Ranking Drops Detected",
                body=alert
            )
        
        return {
            "alerts_sent": True,
            "keywords_affected": len(drops),
            "biggest_drop": drops[0] if drops else None
        }
    
    return {
        "alerts_sent": False,
        "message": "No significant ranking drops detected"
    }

@mcp.resource("notifications://config")
async def get_notification_config() -> str:
    """Get current notification configuration"""
    config = {
        "slack_configured": bool(notifications.slack_webhook_url),
        "email_configured": bool(notifications.smtp_user and notifications.smtp_password),
        "notification_types": [
            "Daily summaries",
            "Weekly reports", 
            "High-value opportunities",
            "Content brief ready",
            "Ranking drop alerts"
        ]
    }
    
    return f"""
    Notification Configuration:
    
    Slack: {'✓ Configured' if config['slack_configured'] else '✗ Not configured'}
    Email: {'✓ Configured' if config['email_configured'] else '✗ Not configured'}
    
    Available Notifications:
    {chr(10).join('- ' + nt for nt in config['notification_types'])}
    """

@mcp.prompt(title="Configure Notification Schedule")
def configure_notifications(frequency: str = "daily") -> str:
    """Set up automated notification schedule"""
    return f"""
    To configure {frequency} notifications for the SEO pipeline:
    
    1. Daily Summary (recommended at 9 AM):
       - New keywords discovered
       - Briefs pending generation
       - Top opportunities
    
    2. Weekly Report (recommended Mondays):
       - Keywords discovered in past week
       - New clusters created
       - Performance metrics
    
    3. Real-time Alerts:
       - High-value opportunities (SV > 10k, KD < 40)
       - Ranking drops > 3 positions
       - New content briefs ready
    
    Use the notification tools to trigger these manually or set up cron jobs for automation.
    """

if __name__ == "__main__":
    mcp.run()