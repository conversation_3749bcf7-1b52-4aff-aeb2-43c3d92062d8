"""
Optimized Letter Generator Service
Handles experience letters and internship letters with proper gender-specific formatting
"""

from datetime import datetime
from typing import Dict, Optional
from enum import Enum

class Gender(Enum):
    MALE = "Mr."
    FEMALE = "Ms."

class LetterType(Enum):
    EXPERIENCE = "experience"
    INTERNSHIP = "internship"

class LetterGenerator:
    """
    Optimized letter generator with proper gender-specific pronoun handling
    """
    
    def __init__(self):
        # Gender-specific data mapping
        self.gender_data = {
            Gender.MALE: {
                "title": "Mr.",
                "possessive": "his",
                "subject": "he", 
                "object": "him",
                "reflexive": "himself"
            },
            Gender.FEMALE: {
                "title": "Ms.",
                "possessive": "her",
                "subject": "she",
                "object": "her", 
                "reflexive": "herself"
            }
        }
    
    def generate_letter(
        self,
        letter_type: LetterType,
        gender: Gender,
        full_name: str,
        company_name: str = "Rapid Innovation",
        start_date: str = None,
        end_date: str = None,
        position: str = None,
        department: str = None,
        **kwargs
    ) -> str:
        """
        Generate optimized letter with proper gender-specific formatting
        
        Args:
            letter_type: Type of letter (experience/internship)
            gender: Gender for pronoun selection
            full_name: Full name of the person
            company_name: Company name
            start_date: Start date (format: "August 05, 2024")
            end_date: End date (format: "August 05, 2024") 
            position: Job position/role
            department: Department name
            **kwargs: Additional parameters
        
        Returns:
            Formatted letter string
        """
        
        # Get gender-specific data
        gender_info = self.gender_data[gender]
        
        # Set defaults
        current_date = datetime.now().strftime("%B %d, %Y")
        start_date = start_date or "August 05, 2024"
        end_date = end_date or "August 05, 2024"
        position = position or ("AI ML Developer" if letter_type == LetterType.INTERNSHIP else "Software Developer")
        department = department or "Development Team"
        
        if letter_type == LetterType.EXPERIENCE:
            return self._generate_experience_letter(
                gender_info, full_name, company_name, current_date,
                start_date, end_date, position, department, **kwargs
            )
        elif letter_type == LetterType.INTERNSHIP:
            return self._generate_internship_letter(
                gender_info, full_name, company_name, current_date,
                start_date, end_date, position, department, **kwargs
            )
        else:
            raise ValueError(f"Unsupported letter type: {letter_type}")
    
    def _generate_experience_letter(
        self, gender_info: Dict, full_name: str, company_name: str,
        current_date: str, start_date: str, end_date: str, 
        position: str, department: str, **kwargs
    ) -> str:
        """Generate experience letter with proper gender formatting"""
        
        return f"""
{company_name}

{current_date}

To Whom It May Concern

This letter is to certify that {gender_info['title']} {full_name} was employed with {company_name} as a {position} in the {department} from {start_date} to {end_date}.

During {gender_info['possessive']} tenure with us, {gender_info['subject']} demonstrated excellent technical skills and professionalism. {gender_info['title']} {full_name.split()[-1]} was actively involved in various projects and consistently delivered high-quality work.

{gender_info['subject'].capitalize()} showed great dedication to {gender_info['possessive']} responsibilities and was a valuable member of our team. {gender_info['possessive'].capitalize()} contributions were significant to the success of the projects {gender_info['subject']} worked on.

We found {gender_info['object']} to be punctual, hardworking, and reliable. {gender_info['subject'].capitalize()} has our best wishes for {gender_info['possessive']} future endeavors.

This letter is issued upon {gender_info['possessive']} request for employment purposes.

Sincerely,

[Signature]
HR Department
{company_name}
        """.strip()
    
    def _generate_internship_letter(
        self, gender_info: Dict, full_name: str, company_name: str,
        current_date: str, start_date: str, end_date: str,
        position: str, department: str, **kwargs
    ) -> str:
        """Generate internship letter with proper gender formatting"""
        
        return f"""
{company_name}

{current_date}

To Whom It May Concern

This letter is to certify that {gender_info['title']} {full_name} has completed {gender_info['possessive']} internship with {company_name}. {gender_info['possessive'].capitalize()} internship tenure was from {start_date} to {end_date}. {gender_info['subject'].capitalize()} was working with us as an {position} and was actively & diligently involved in the projects and tasks assigned to {gender_info['object']}.

During this time, we found {gender_info['object']} to be punctual and hardworking.

We wish {gender_info['object']} a bright future.

Sincerely,

[Signature]
HR Department
{company_name}
        """.strip()

# Usage Examples and Testing
def test_letter_generator():
    """Test the letter generator with different scenarios"""
    
    generator = LetterGenerator()
    
    # Test Male Internship Letter
    male_internship = generator.generate_letter(
        letter_type=LetterType.INTERNSHIP,
        gender=Gender.MALE,
        full_name="Naman Nagi",
        start_date="August 05, 2024",
        end_date="August 05, 2024"
    )
    
    # Test Female Experience Letter  
    female_experience = generator.generate_letter(
        letter_type=LetterType.EXPERIENCE,
        gender=Gender.FEMALE,
        full_name="Priya Sharma",
        position="Senior Developer",
        start_date="January 15, 2023",
        end_date="December 31, 2024"
    )
    
    return {
        "male_internship": male_internship,
        "female_experience": female_experience
    }

# Quick generation functions for common use cases
def generate_male_internship_letter(full_name: str, **kwargs) -> str:
    """Quick function for male internship letters"""
    generator = LetterGenerator()
    return generator.generate_letter(
        LetterType.INTERNSHIP, Gender.MALE, full_name, **kwargs
    )

def generate_female_internship_letter(full_name: str, **kwargs) -> str:
    """Quick function for female internship letters"""
    generator = LetterGenerator()
    return generator.generate_letter(
        LetterType.INTERNSHIP, Gender.FEMALE, full_name, **kwargs
    )

def generate_male_experience_letter(full_name: str, **kwargs) -> str:
    """Quick function for male experience letters"""
    generator = LetterGenerator()
    return generator.generate_letter(
        LetterType.EXPERIENCE, Gender.MALE, full_name, **kwargs
    )

def generate_female_experience_letter(full_name: str, **kwargs) -> str:
    """Quick function for female experience letters"""
    generator = LetterGenerator()
    return generator.generate_letter(
        LetterType.EXPERIENCE, Gender.FEMALE, full_name, **kwargs
    )

if __name__ == "__main__":
    # Test the generator
    test_results = test_letter_generator()
    
    print("=== MALE INTERNSHIP LETTER ===")
    print(test_results["male_internship"])
    print("\n" + "="*50 + "\n")
    
    print("=== FEMALE EXPERIENCE LETTER ===") 
    print(test_results["female_experience"])
