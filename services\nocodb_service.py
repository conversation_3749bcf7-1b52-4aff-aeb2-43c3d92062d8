import httpx
from typing import Dict, List, Optional, Any
from config.settings import settings
import json

class NocoDBService:
    def __init__(self):
        self.base_url = settings.nocodb_api_url
        self.api_token = settings.nocodb_api_token
        self.workspace_id = settings.nocodb_workspace_id
        self.base_id = settings.nocodb_base_id
        self.headers = {
            "xc-token": self.api_token,
            "Content-Type": "application/json"
        }
    
    async def get_table_data(self, table_name: str, filters: Optional[Dict] = None) -> List[Dict]:
        """Fetch data from a NocoDB table"""
        async with httpx.AsyncClient() as client:
            params = {}
            if filters:
                params["where"] = json.dumps(filters)

            # For hosted NocoDB, use the correct API structure
            url = f"{self.base_url}/api/v1/db/data/{self.workspace_id}/{self.base_id}/{table_name}"

            response = await client.get(
                url,
                headers=self.headers,
                params=params
            )
            response.raise_for_status()
            return response.json()["list"]
    
    async def create_record(self, table_name: str, data: Dict) -> Dict:
        """Create a new record in NocoDB table"""
        async with httpx.AsyncClient() as client:
            url = f"{self.base_url}/api/v1/db/data/{self.workspace_id}/{self.base_id}/{table_name}"
            response = await client.post(
                url,
                headers=self.headers,
                json=data
            )
            response.raise_for_status()
            return response.json()

    async def update_record(self, table_name: str, record_id: str, data: Dict) -> Dict:
        """Update a record in NocoDB table"""
        async with httpx.AsyncClient() as client:
            url = f"{self.base_url}/api/v1/db/data/{self.workspace_id}/{self.base_id}/{table_name}/{record_id}"
            response = await client.patch(
                url,
                headers=self.headers,
                json=data
            )
            response.raise_for_status()
            return response.json()

    async def bulk_create(self, table_name: str, records: List[Dict]) -> List[Dict]:
        """Bulk create records in NocoDB table"""
        async with httpx.AsyncClient() as client:
            url = f"{self.base_url}/api/v1/db/data/bulk/{self.workspace_id}/{self.base_id}/{table_name}"
            response = await client.post(
                url,
                headers=self.headers,
                json=records
            )
            response.raise_for_status()
            return response.json()