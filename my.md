process_seed_keyword
Purpose: Processes a single keyword by ID
Input: Just the keyword ID (like 1, 2, or 3)
What it does:
Takes a raw keyword (like "seo tools")
Normalizes it (lowercase, trim spaces)
Classifies the search intent (informational, commercial, transactional)
Returns processed data ready for keyword expansion
🔧 batch_process_seeds
Purpose: Processes ALL pending keywords at once
Input: None (empty)
What it does:
Finds all pending keywords in the system
Processes each one using the same logic as single processing
Returns a summary of all processed keywords