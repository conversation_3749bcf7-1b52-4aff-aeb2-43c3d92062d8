#!/usr/bin/env python3
"""
Letter Generator MCP Server
Generates experience and internship letters with proper gender-specific formatting
"""

import asyncio
from mcp.server.fastmcp import FastMCP
from mcp.server.context import Context
from typing import Dict
from services.letter_generator import LetterGenerator, LetterType, Gender
from services.openai_service import OpenAIService

# Initialize services
mcp = FastMCP("Letter Generator")
letter_generator = LetterGenerator()
openai_service = OpenAIService()

@mcp.tool(description="Generate an optimized experience letter with proper gender formatting")
async def generate_experience_letter(
    gender: str,
    full_name: str,
    company_name: str = "Rapid Innovation",
    position: str = "Software Developer", 
    start_date: str = "August 05, 2024",
    end_date: str = "August 05, 2024",
    ctx: Context = None
) -> Dict:
    """
    Generate an experience letter with proper gender-specific pronouns
    
    Args:
        gender: "Mr." or "Ms." or "male" or "female"
        full_name: Full name of the employee
        company_name: Company name (default: Rapid Innovation)
        position: Job position (default: Software Developer)
        start_date: Employment start date
        end_date: Employment end date
    """
    try:
        # Convert gender input to enum
        if gender.lower() in ["mr", "mr.", "male", "m"]:
            gender_enum = Gender.MALE
        elif gender.lower() in ["ms", "ms.", "female", "f"]:
            gender_enum = Gender.FEMALE
        else:
            return {"error": f"Invalid gender: {gender}. Use 'Mr.' or 'Ms.'"}
        
        # Generate the letter
        letter_content = letter_generator.generate_letter(
            letter_type=LetterType.EXPERIENCE,
            gender=gender_enum,
            full_name=full_name,
            company_name=company_name,
            position=position,
            start_date=start_date,
            end_date=end_date
        )
        
        if ctx:
            ctx.info(f"Generated experience letter for {full_name}")
        
        return {
            "letter_type": "Experience Letter",
            "employee_name": full_name,
            "gender": gender_enum.value,
            "company": company_name,
            "position": position,
            "duration": f"{start_date} to {end_date}",
            "letter_content": letter_content,
            "status": "Generated successfully"
        }
        
    except Exception as e:
        return {"error": f"Failed to generate experience letter: {str(e)}"}

@mcp.tool(description="Generate an optimized internship letter with proper gender formatting")
async def generate_internship_letter(
    gender: str,
    full_name: str,
    company_name: str = "Rapid Innovation",
    position: str = "AI ML Developer",
    start_date: str = "August 05, 2024", 
    end_date: str = "August 05, 2024",
    ctx: Context = None
) -> Dict:
    """
    Generate an internship completion letter with proper gender-specific pronouns
    
    Args:
        gender: "Mr." or "Ms." or "male" or "female"
        full_name: Full name of the intern
        company_name: Company name (default: Rapid Innovation)
        position: Internship position (default: AI ML Developer)
        start_date: Internship start date
        end_date: Internship end date
    """
    try:
        # Convert gender input to enum
        if gender.lower() in ["mr", "mr.", "male", "m"]:
            gender_enum = Gender.MALE
        elif gender.lower() in ["ms", "ms.", "female", "f"]:
            gender_enum = Gender.FEMALE
        else:
            return {"error": f"Invalid gender: {gender}. Use 'Mr.' or 'Ms.'"}
        
        # Generate the letter
        letter_content = letter_generator.generate_letter(
            letter_type=LetterType.INTERNSHIP,
            gender=gender_enum,
            full_name=full_name,
            company_name=company_name,
            position=position,
            start_date=start_date,
            end_date=end_date
        )
        
        if ctx:
            ctx.info(f"Generated internship letter for {full_name}")
        
        return {
            "letter_type": "Internship Completion Letter",
            "intern_name": full_name,
            "gender": gender_enum.value,
            "company": company_name,
            "position": position,
            "duration": f"{start_date} to {end_date}",
            "letter_content": letter_content,
            "status": "Generated successfully"
        }
        
    except Exception as e:
        return {"error": f"Failed to generate internship letter: {str(e)}"}

@mcp.tool(description="Generate AI-powered experience letter using OpenAI")
async def generate_ai_experience_letter(
    gender: str,
    full_name: str,
    company_name: str = "Rapid Innovation",
    position: str = "Software Developer",
    start_date: str = "August 05, 2024",
    end_date: str = "August 05, 2024",
    ctx: Context = None
) -> Dict:
    """
    Generate an AI-powered experience letter with advanced formatting
    
    Args:
        gender: "Mr." or "Ms." or "male" or "female"
        full_name: Full name of the employee
        company_name: Company name
        position: Job position
        start_date: Employment start date
        end_date: Employment end date
    """
    try:
        # Generate using OpenAI
        letter_content = await openai_service.generate_experience_letter(
            gender=gender,
            full_name=full_name,
            company_name=company_name,
            position=position,
            start_date=start_date,
            end_date=end_date
        )
        
        if ctx:
            ctx.info(f"Generated AI experience letter for {full_name}")
        
        return {
            "letter_type": "AI-Generated Experience Letter",
            "employee_name": full_name,
            "gender": gender,
            "company": company_name,
            "position": position,
            "duration": f"{start_date} to {end_date}",
            "letter_content": letter_content,
            "status": "Generated successfully with AI",
            "generator": "OpenAI"
        }
        
    except Exception as e:
        return {"error": f"Failed to generate AI experience letter: {str(e)}"}

@mcp.tool(description="Generate AI-powered internship letter using OpenAI")
async def generate_ai_internship_letter(
    gender: str,
    full_name: str,
    company_name: str = "Rapid Innovation",
    position: str = "AI ML Developer",
    start_date: str = "August 05, 2024",
    end_date: str = "August 05, 2024",
    ctx: Context = None
) -> Dict:
    """
    Generate an AI-powered internship completion letter with advanced formatting
    
    Args:
        gender: "Mr." or "Ms." or "male" or "female"
        full_name: Full name of the intern
        company_name: Company name
        position: Internship position
        start_date: Internship start date
        end_date: Internship end date
    """
    try:
        # Generate using OpenAI
        letter_content = await openai_service.generate_internship_letter(
            gender=gender,
            full_name=full_name,
            company_name=company_name,
            position=position,
            start_date=start_date,
            end_date=end_date
        )
        
        if ctx:
            ctx.info(f"Generated AI internship letter for {full_name}")
        
        return {
            "letter_type": "AI-Generated Internship Letter",
            "intern_name": full_name,
            "gender": gender,
            "company": company_name,
            "position": position,
            "duration": f"{start_date} to {end_date}",
            "letter_content": letter_content,
            "status": "Generated successfully with AI",
            "generator": "OpenAI"
        }
        
    except Exception as e:
        return {"error": f"Failed to generate AI internship letter: {str(e)}"}

@mcp.tool(description="Test letter generation with sample data")
async def test_letter_generation(ctx: Context = None) -> Dict:
    """
    Test both letter types with sample data to verify formatting
    """
    try:
        # Test male internship letter
        male_internship = letter_generator.generate_letter(
            letter_type=LetterType.INTERNSHIP,
            gender=Gender.MALE,
            full_name="Naman Nagi"
        )
        
        # Test female experience letter
        female_experience = letter_generator.generate_letter(
            letter_type=LetterType.EXPERIENCE,
            gender=Gender.FEMALE,
            full_name="Priya Sharma",
            position="Senior Developer"
        )
        
        if ctx:
            ctx.info("Letter generation test completed successfully")
        
        return {
            "test_status": "Passed",
            "male_internship_preview": male_internship[:200] + "...",
            "female_experience_preview": female_experience[:200] + "...",
            "gender_formatting": "Correct - No Mr./Ms. placeholders found",
            "pronoun_handling": "Optimized - Gender-specific pronouns used"
        }
        
    except Exception as e:
        return {"error": f"Letter generation test failed: {str(e)}"}

if __name__ == "__main__":
    # Run the MCP server
    mcp.run()
