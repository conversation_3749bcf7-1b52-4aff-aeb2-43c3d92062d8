# services/notification_service.py
import httpx
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON>ultipart
from typing import Optional, List
from config.settings import settings

class NotificationService:
    async def send_slack_message(self, message: str, channel: Optional[str] = None):
        """Send a message to <PERSON>lack"""
        if not settings.slack_webhook_url:
            return
        
        async with httpx.AsyncClient() as client:
            payload = {"text": message}
            if channel:
                payload["channel"] = channel
            
            await client.post(settings.slack_webhook_url, json=payload)
    
    async def send_email(self, to: List[str], subject: str, body: str):
        """Send an email notification"""
        if not settings.smtp_user or not settings.smtp_password:
            return
        
        msg = MIMEMultipart()
        msg["From"] = settings.smtp_user
        msg["To"] = ", ".join(to)
        msg["Subject"] = subject
        
        msg.attach(MIMEText(body, "plain"))
        
        with smtplib.SMTP(settings.smtp_host, settings.smtp_port) as server:
            server.starttls()
            server.login(settings.smtp_user, settings.smtp_password)
            server.send_message(msg)