from openai import Async<PERSON><PERSON><PERSON><PERSON>
from typing import List, Dict
from config.settings import settings
import json

class OpenAIService:
    def __init__(self):
        self.client = AsyncOpenAI(api_key=settings.openai_api_key)
        self.model = settings.openai_model
    
    async def normalize_keyword(self, keyword: str) -> str:
        """Normalize a keyword using AI"""
        prompt = f"""
        Normalize the following keyword by:
        1. Converting to lowercase
        2. Removing extra spaces
        3. Fixing common typos
        4. Removing unnecessary words
        
        Keyword: {keyword}
        
        Return only the normalized keyword.
        """
        
        response = await self.client.chat.completions.create(
            model=self.model,
            messages=[{"role": "user", "content": prompt}],
            temperature=0
        )
        
        return response.choices[0].message.content.strip()

    async def generate_experience_letter(
        self,
        gender: str,
        full_name: str,
        company_name: str = "Rapid Innovation",
        position: str = "Software Developer",
        start_date: str = None,
        end_date: str = None,
        **kwargs
    ) -> str:
        """Generate an experience letter with proper gender-specific formatting"""

        # Set gender-specific pronouns
        if gender.lower() in ["mr", "mr.", "male", "m"]:
            title = "Mr."
            possessive = "his"
            subject = "he"
            object_pronoun = "him"
        elif gender.lower() in ["ms", "ms.", "female", "f"]:
            title = "Ms."
            possessive = "her"
            subject = "she"
            object_pronoun = "her"
        else:
            title = "Mr./Ms."
            possessive = "their"
            subject = "they"
            object_pronoun = "them"

        prompt = f"""
        Generate a professional experience letter with the following details:

        - Name: {title} {full_name}
        - Company: {company_name}
        - Position: {position}
        - Start Date: {start_date or "August 05, 2024"}
        - End Date: {end_date or "August 05, 2024"}

        Use these gender-specific pronouns consistently:
        - Possessive: {possessive}
        - Subject: {subject}
        - Object: {object_pronoun}

        Format as a formal business letter with:
        1. Company letterhead
        2. Date
        3. "To Whom It May Concern"
        4. Employment confirmation
        5. Performance highlights
        6. Professional closing
        7. Signature line

        Ensure NO gender placeholder text like "Mr./Ms." appears in the final letter.
        Use only the specific title: {title}
        """

        response = await self.client.chat.completions.create(
            model=self.model,
            messages=[{"role": "user", "content": prompt}],
            temperature=0.3
        )

        return response.choices[0].message.content.strip()

    async def generate_internship_letter(
        self,
        gender: str,
        full_name: str,
        company_name: str = "Rapid Innovation",
        position: str = "AI ML Developer",
        start_date: str = None,
        end_date: str = None,
        **kwargs
    ) -> str:
        """Generate an internship completion letter with proper gender-specific formatting"""

        # Set gender-specific pronouns
        if gender.lower() in ["mr", "mr.", "male", "m"]:
            title = "Mr."
            possessive = "his"
            subject = "he"
            object_pronoun = "him"
        elif gender.lower() in ["ms", "ms.", "female", "f"]:
            title = "Ms."
            possessive = "her"
            subject = "she"
            object_pronoun = "her"
        else:
            title = "Mr./Ms."
            possessive = "their"
            subject = "they"
            object_pronoun = "them"

        prompt = f"""
        Generate a professional internship completion letter with the following details:

        - Name: {title} {full_name}
        - Company: {company_name}
        - Position: {position}
        - Start Date: {start_date or "August 05, 2024"}
        - End Date: {end_date or "August 05, 2024"}

        Use these gender-specific pronouns consistently:
        - Possessive: {possessive}
        - Subject: {subject}
        - Object: {object_pronoun}

        Format as a formal internship completion certificate with:
        1. Company letterhead
        2. Date
        3. "To Whom It May Concern"
        4. Internship completion confirmation
        5. Performance evaluation
        6. Future wishes
        7. Professional closing
        8. Signature line

        Ensure NO gender placeholder text like "Mr./Ms." appears in the final letter.
        Use only the specific title: {title}
        """

        response = await self.client.chat.completions.create(
            model=self.model,
            messages=[{"role": "user", "content": prompt}],
            temperature=0.3
        )

        return response.choices[0].message.content.strip()
    
    async def classify_intent(self, keywords: List[str]) -> List[Dict[str, str]]:
        """Classify search intent for keywords"""
        prompt = f"""
        Classify the search intent for each keyword as one of:
        - informational
        - commercial
        - transactional
        - navigational
        
        Also assign a funnel stage:
        - awareness
        - consideration
        - conversion
        
        Keywords:
        {json.dumps(keywords, indent=2)}
        
        Return a JSON array with objects containing:
        - keyword
        - intent
        - funnel_stage
        """
        
        response = await self.client.chat.completions.create(
            model=self.model,
            messages=[{"role": "user", "content": prompt}],
            temperature=0,
            response_format={"type": "json_object"}
        )
        
        return json.loads(response.choices[0].message.content)
    
    async def cluster_keywords(self, keywords: List[Dict]) -> List[Dict]:
        """Cluster keywords into pillar topics and subtopics"""
        keyword_list = [k["keyword"] for k in keywords]
        
        prompt = f"""
        Cluster these keywords into pillar topics and subtopics.
        Create 3-5 main clusters based on semantic similarity.
        
        Keywords:
        {json.dumps(keyword_list, indent=2)}
        
        Return a JSON array with objects containing:
        - pillar_keyword: the main topic
        - subtopics: array of related keywords
        - total_search_volume: sum of all keyword volumes
        - avg_keyword_difficulty: average difficulty
        
        Consider the metrics when clustering.
        """
        
        response = await self.client.chat.completions.create(
            model=self.model,
            messages=[{"role": "user", "content": prompt}],
            temperature=0.3,
            response_format={"type": "json_object"}
        )
        
        clusters = json.loads(response.choices[0].message.content).get("clusters", [])
        
        # Calculate actual metrics
        for cluster in clusters:
            cluster_keywords = [k for k in keywords if k["keyword"] in cluster["subtopics"]]
            cluster["total_search_volume"] = sum(k.get("search_volume", 0) for k in cluster_keywords)
            cluster["avg_keyword_difficulty"] = sum(k.get("keyword_difficulty", 0) for k in cluster_keywords) / len(cluster_keywords) if cluster_keywords else 0
        
        return clusters
    
    async def generate_content_brief(self, pillar: str, subtopics: List[str]) -> str:
        """Generate a content brief for a keyword cluster"""
        prompt = f"""
        Create a comprehensive SEO content brief for:
        
        Pillar Topic: {pillar}
        Subtopics: {', '.join(subtopics)}
        
        Include:
        1. Title suggestions (3 options)
        2. Meta description (155 chars)
        3. Content outline with H2 and H3 headings
        4. Key points to cover under each section
        5. Word count recommendation
        6. Internal linking opportunities
        7. FAQs to include
        8. Call-to-action suggestions
        
        Format as a structured brief that a content writer can follow.
        """
        
        response = await self.client.chat.completions.create(
            model=self.model,
            messages=[{"role": "user", "content": prompt}],
            temperature=0.7
        )
        
        return response.choices[0].message.content

