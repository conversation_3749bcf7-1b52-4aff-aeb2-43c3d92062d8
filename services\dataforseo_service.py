import httpx
import base64
from typing import Dict, List
from config.settings import settings, Settings

class DataForSEOService:
    def __init__(self, settings: Settings = None):
        self.settings = settings or Settings()
        self.base_url = "https://api.dataforseo.com/v3"

        # Set up authentication headers
        auth_string = f"{self.settings.dataforseo_login}:{self.settings.dataforseo_password}"
        auth_bytes = auth_string.encode('ascii')
        auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
        self.headers = {
            "Authorization": f"Basic {auth_b64}",
            "Content-Type": "application/json"
        }

        if self.settings.proxy_host:
            proxy_user = self.settings.proxy_user
            if self.settings.proxy_session_token:
                proxy_user = f"{proxy_user}-session-{self.settings.proxy_session_token}"
            
            proxy_url = f"http://{proxy_user}:{self.settings.proxy_pass}@{self.settings.proxy_host}:{self.settings.proxy_port}"
            self.proxy = {"http://": proxy_url, "https://": proxy_url}
            self.client = httpx.AsyncClient(proxies=self.proxy)
        else:
            self.client = httpx.AsyncClient()
    
    async def get_related_keywords(self, keyword: str, location_code: int = 2840) -> List[Dict]:
        """Get related keywords with metrics"""
        async with httpx.AsyncClient() as client:
            data = [{
                "keyword": keyword,
                "location_code": location_code,  # USA
                "language_code": "en",
                "limit": 100,
                "filters": [
                    ["keyword_info.search_volume", ">=", settings.min_search_volume],
                    ["keyword_info.competition_level", "<=", settings.max_keyword_difficulty / 100]
                ]
            }]
            
            response = await client.post(
                f"{self.base_url}/keywords_data/google_ads/keywords_for_keyword/live",
                headers=self.headers,
                json=data
            )
            response.raise_for_status()
            
            results = []
            for task in response.json().get("tasks", []):
                if task.get("result"):
                    for item in task["result"]:
                        results.extend(item.get("items", []))
            
            return results
    
    async def get_serp_results(self, keyword: str, location_code: int = 2840) -> List[Dict]:
        """Get SERP results for a keyword"""
        async with httpx.AsyncClient() as client:
            data = [{
                "keyword": keyword,
                "location_code": location_code,
                "language_code": "en",
                "device": "desktop",
                "os": "windows",
                "depth": 10
            }]
            
            response = await client.post(
                f"{self.base_url}/serp/google/organic/live/regular",
                headers=self.headers,
                json=data
            )
            response.raise_for_status()
            
            results = []
            for task in response.json().get("tasks", []):
                if task.get("result"):
                    for item in task["result"]:
                        results.extend(item.get("items", []))
            
            return results
    
    async def get_keyword_metrics(self, keywords: List[str], location_code: int = 2840) -> Dict[str, Dict]:
        """Get metrics for multiple keywords"""
        async with httpx.AsyncClient() as client:
            data = [{
                "keywords": keywords,
                "location_code": location_code,
                "language_code": "en"
            }]
            
            response = await client.post(
                f"{self.base_url}/keywords_data/google_ads/search_volume/live",
                headers=self.headers,
                json=data
            )
            response.raise_for_status()
            
            metrics = {}
            for task in response.json().get("tasks", []):
                if task.get("result"):
                    for item in task["result"]:
                        keyword = item.get("keyword")
                        metrics[keyword] = {
                            "search_volume": item.get("search_volume", 0),
                            "competition": item.get("competition", 0),
                            "cpc": item.get("cpc", 0)
                        }
            
            return metrics