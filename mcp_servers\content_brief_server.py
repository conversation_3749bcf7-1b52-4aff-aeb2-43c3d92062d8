# mcp_servers/content_brief_server.py
from mcp.server.fastmcp import FastMCP, Context
from services.sqlite_services import SQLiteService
from services.openai_service import OpenAIService
from services.notification_service import NotificationService
from typing import Dict
import json
from datetime import datetime

mcp = FastMCP("SEO Content Brief Generator")
nocodb = SQLiteService()
openai_service = OpenAIService()
notifications = NotificationService()

@mcp.tool(description="Generate a content brief for an approved cluster")
async def generate_content_brief(cluster_id: str, ctx: Context) -> Dict:
    """
    Generate a comprehensive content brief for a keyword cluster
    """
    # Get cluster data
    clusters = await nocodb.get_table_data("clusters", {"id": cluster_id})
    if not clusters:
        return {"error": "Cluster not found"}
    
    cluster = clusters[0]
    
    if not cluster.get("approved_for_brief"):
        return {"error": "Cluster not approved for brief generation"}
    
    ctx.info(f"Generating brief for: {cluster['pillar_keyword']}")
    
    # Parse subtopics
    subtopics = json.loads(cluster.get("subtopics", "[]"))
    
    # Generate the brief using OpenAI
    brief_content = await openai_service.generate_content_brief(
        cluster["pillar_keyword"],
        subtopics
    )
    
    # Update cluster with brief
    await nocodb.update_record("clusters", cluster_id, {
        "brief": brief_content,
        "brief_generated_at": datetime.now().isoformat()
    })
    
    # Send notifications
    await notifications.send_slack_message(
        f"📝 New content brief generated for: {cluster['pillar_keyword']}\n"
        f"Total Search Volume: {cluster['total_search_volume']:,}\n"
        f"View in NocoDB: {nocodb.base_url}/dashboard/clusters/{cluster_id}"
    )
    
    return {
        "cluster_id": cluster_id,
        "pillar": cluster["pillar_keyword"],
        "brief_preview": brief_content[:500] + "...",
        "notification_sent": True
    }

@mcp.tool(description="Batch generate briefs for all approved clusters")
async def batch_generate_briefs(ctx: Context) -> Dict:
    """Generate briefs for all approved clusters without briefs"""
    # Get approved clusters without briefs
    clusters = await nocodb.get_table_data("clusters", {"approved_for_brief": True})
    clusters_needing_briefs = [c for c in clusters if not c.get("brief")]
    
    ctx.info(f"Found {len(clusters_needing_briefs)} clusters needing briefs")
    
    results = []
    for idx, cluster in enumerate(clusters_needing_briefs):
        ctx.report_progress(idx, len(clusters_needing_briefs))
        result = await generate_content_brief(str(cluster["id"]), ctx)
        results.append(result)
    
    # Send summary notification
    if results:
        await notifications.send_email(
            to=["<EMAIL>"],
            subject=f"SEO Pipeline: {len(results)} New Content Briefs Generated",
            body=f"""
            The SEO pipeline has generated {len(results)} new content briefs.

            Clusters processed:
            """ + '\n'.join(f"- {r['pillar']}" for r in results if 'pillar' in r) + f"""

            Access the briefs in NocoDB: {nocodb.base_url}/dashboard/clusters
            """
        )
    
    return {
        "total_generated": len(results),
        "clusters": [r.get("pillar", "Unknown") for r in results]
    }

@mcp.tool(description="Approve a cluster for brief generation")
async def approve_cluster_for_brief(cluster_id: str, notes: str = "") -> Dict:
    """Mark a cluster as approved for content brief generation"""
    clusters = await nocodb.get_table_data("clusters", {"id": cluster_id})
    if not clusters:
        return {"error": "Cluster not found"}
    
    await nocodb.update_record("clusters", cluster_id, {
        "approved_for_brief": True,
        "approval_notes": notes,
        "approved_at": datetime.now().isoformat()
    })
    
    return {
        "cluster_id": cluster_id,
        "status": "Approved for brief generation",
        "notes": notes
    }

@mcp.resource("briefs://pending-approval")
async def get_clusters_pending_approval() -> str:
    """Get clusters that haven't been approved for briefs yet"""
    clusters = await nocodb.get_table_data("clusters", {"approved_for_brief": False})
    
    high_potential = [c for c in clusters if c.get("total_search_volume", 0) > 5000 and c.get("avg_keyword_difficulty", 100) < 50]
    
    return f"""
    Clusters Pending Brief Approval:
    Total: {len(clusters)}
    High Potential (SV > 5000, KD < 50): {len(high_potential)}

    Top candidates:
    """ + '\n'.join(f"- {c['pillar_keyword']} (SV: {c['total_search_volume']:,}, KD: {c['avg_keyword_difficulty']:.1f})"
                     for c in high_potential[:10]) + """
    """

if __name__ == "__main__":
    mcp.run()