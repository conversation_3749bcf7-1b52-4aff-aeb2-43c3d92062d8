# seed_server_wrapper.py (in project root, NOT in mcp_servers)
import sys
import os

# Add the project root to Python path FIRST
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# Now we can import everything properly
from mcp.server.fastmcp import FastMCP
from typing import Dict
import asyncio

# Copy all the server code here
mcp = FastMCP("SEO Seed Keyword Processor")

@mcp.tool()
async def process_seed_keyword(keyword_id: str) -> Dict:
    """Process a new seed keyword: normalize it and classify its intent

    Args:
        keyword_id: The ID of the keyword to process (e.g., "1", "2", "3")
    """
    print(f"DEBUG: Received keyword_id: '{keyword_id}' (type: {type(keyword_id)})")
    # Mock data for demonstration (replace with real database when available)
    mock_keywords = {
        "1": {"raw_keyword": "seo tools", "id": "1"},
        "2": {"raw_keyword": "best keyword research", "id": "2"},
        "3": {"raw_keyword": "content marketing strategy", "id": "3"}
    }

    if keyword_id not in mock_keywords:
        return {"error": f"Keyword with ID {keyword_id} not found. Available IDs: {list(mock_keywords.keys())}"}

    record = mock_keywords[keyword_id]
    raw_keyword = record.get("raw_keyword", "")

    # Mock normalization and intent classification for demo
    normalized = raw_keyword.lower().strip()

    # Simple intent classification based on keywords
    if any(word in normalized for word in ["best", "top", "review"]):
        intent = "commercial"
    elif any(word in normalized for word in ["buy", "price", "cost"]):
        intent = "transactional"
    elif any(word in normalized for word in ["how", "what", "why", "guide"]):
        intent = "informational"
    else:
        intent = "informational"

    return {
        "keyword_id": keyword_id,
        "raw_keyword": raw_keyword,
        "normalized": normalized,
        "intent": intent,
        "status": "Ready for Expansion",
        "note": "This is a demo with mock data. Connect to real database for production use."
    }

@mcp.resource("seed-keywords://pending")
async def get_pending_seeds() -> str:
    """Get all pending seed keywords"""
    # Mock pending keywords for demonstration
    mock_pending = [
        {"id": "1", "raw_keyword": "seo tools", "status": "New"},
        {"id": "2", "raw_keyword": "best keyword research", "status": "New"},
        {"id": "3", "raw_keyword": "content marketing strategy", "status": "New"}
    ]

    return f"""Found {len(mock_pending)} pending seed keywords:

{chr(10).join(f"- ID {k['id']}: {k['raw_keyword']}" for k in mock_pending)}

Note: This is demo data. Connect to real database for production use."""

@mcp.tool()
async def batch_process_seeds() -> Dict:
    """Batch process all pending seed keywords

    Processes all pending keywords in the system at once.
    No parameters required."""
    # Mock pending keywords for batch processing
    pending_ids = ["1", "2", "3"]

    results = []
    for keyword_id in pending_ids:
        result = await process_seed_keyword(keyword_id)
        results.append(result)
        await asyncio.sleep(0.1)  # Small delay for demo

    return {
        "processed": len(results),
        "results": results,
        "note": "Batch processed mock data. Connect to real database for production use."
    }

if __name__ == "__main__":
    mcp.run()