# mcp_servers/performance_tracker_server.py
from mcp.server.fastmcp import FastMCP, Context
from services.nocodb_service import NocoDBService
from services.notification_service import NotificationService
from typing import Dict, List
from datetime import datetime

mcp = FastMCP("SEO Performance Tracker")
nocodb = NocoDBService()
notifications = NotificationService()

@mcp.tool(description="Track keyword rankings and performance")
async def track_keyword_performance(keywords: List[str], ctx: Context) -> Dict:
    """
    Track current rankings for a list of keywords (mock version)
    """
    ctx.info(f"Tracking performance for {len(keywords)} keywords")

    # Mock tracking data
    import random
    tracking_data = []
    position_drops = []

    for keyword in keywords:
        # Generate mock performance data
        current_position = random.randint(1, 50)
        previous_position = random.randint(1, 50)
        search_volume = random.randint(100, 10000)
        cpc = round(random.uniform(0.5, 5.0), 2)

        # Calculate position change
        position_change = previous_position - current_position  # Positive = improvement

        tracking_record = {
            "keyword": keyword,
            "current_position": current_position,
            "previous_position": previous_position,
            "position_change": position_change,
            "search_volume": search_volume,
            "cpc": cpc,
            "timestamp": "2024-01-15T10:00:00Z"
        }

        tracking_data.append(tracking_record)

        # Check for significant drops (already added to position_drops above)
        ctx.info(f"Tracked '{keyword}': Position {current_position} (change: {position_change:+d})")

    # Mock: Save tracking data (would normally save to database)
    ctx.info(f"Would save {len(tracking_data)} tracking records to database")

    # Mock: Send alerts for significant drops
    if position_drops:
        ctx.info(f"⚠️ SEO Alert: {len(position_drops)} keywords dropped significantly")
        for drop in position_drops[:3]:  # Show first 3
            ctx.info(f"• {drop['keyword']}: #{drop['previous']} → #{drop['current']} (↓{drop['change']})")

    return {
        "tracked": len(tracking_data),
        "alerts": len(position_drops),
        "sample_data": tracking_data[:3],
        "position_drops": position_drops
    }

@mcp.tool(description="Run quarterly performance review")
async def quarterly_performance_review(ctx: Context) -> Dict:
    """
    Run a comprehensive quarterly performance review
    """
    # Get all clusters
    clusters = await nocodb.get_table_data("clusters")
    
    # Extract all pillar keywords
    pillar_keywords = [c["pillar_keyword"] for c in clusters if c.get("pillar_keyword")]
    
    ctx.info(f"Running quarterly review for {len(pillar_keywords)} pillar keywords")
    
    # Track performance
    tracking_result = await track_keyword_performance(pillar_keywords, ctx)
    
    # Get all tracking data for analysis
    all_tracking = await nocodb.get_table_data("performance_tracking")
    
    # Calculate summary statistics
    total_improved = len([t for t in all_tracking if t.get("position_change", 0) < 0])
    total_declined = len([t for t in all_tracking if t.get("position_change", 0) > 0])
    total_stable = len([t for t in all_tracking if t.get("position_change", 0) == 0])
    
    # Generate report
    report = f"""
    Quarterly SEO Performance Review
    ================================
    Date: {datetime.now().strftime('%Y-%m-%d')}
    
    Keywords Tracked: {len(pillar_keywords)}
    
    Performance Summary:
    - Improved: {total_improved} keywords
    - Declined: {total_declined} keywords  
    - Stable: {total_stable} keywords
    
    Keywords with Significant Drops: {tracking_result['position_drops']}
    
    Recommendations:
    1. Review and update content for declined keywords
    2. Analyze competitor changes for dropped positions
    3. Continue monitoring weekly for next quarter
    """
    
    # Send comprehensive report
    await notifications.send_email(
        to=["<EMAIL>", "<EMAIL>"],
        subject="Quarterly SEO Performance Review",
        body=report
    )
    
    return {
        "review_date": datetime.now().isoformat(),
        "keywords_tracked": len(pillar_keywords),
        "summary": {
            "improved": total_improved,
            "declined": total_declined,
            "stable": total_stable
        },
        "report_sent": True
    }

@mcp.resource("performance://dashboard")
async def get_performance_dashboard() -> str:
    """Get current performance metrics dashboard"""
    # Get recent tracking data
    tracking_data = await nocodb.get_table_data("performance_tracking")
    
    # Get last 30 days of data
    recent_data = [t for t in tracking_data 
                   if datetime.fromisoformat(t["tracked_at"]) > datetime.now().replace(day=1)]
    
    if not recent_data:
        return "No recent tracking data available"
    
    # Calculate metrics
    avg_position = sum(t["current_position"] for t in recent_data) / len(recent_data)
    improved = len([t for t in recent_data if t["position_change"] < 0])
    declined = len([t for t in recent_data if t["position_change"] > 0])
    
    return f"""
    SEO Performance Dashboard
    ========================
    Period: Last 30 Days
    
    Average Position: {avg_position:.1f}
    Keywords Tracked: {len(set(t["keyword"] for t in recent_data))}
    
    Movement:
    - Improved: {improved} ↑
    - Declined: {declined} ↓
    - Stable: {len(recent_data) - improved - declined} →
    
    Last Update: {max(t["tracked_at"] for t in recent_data)}
    """

if __name__ == "__main__":
    mcp.run()