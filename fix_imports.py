import os
from pathlib import Path

# Import fix to add at the top of each server file
IMPORT_FIX = """import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

"""

# Fix all server files
server_dir = Path("mcp_servers")
for file in server_dir.glob("*.py"):
    if file.name != "__init__.py":
        with open(file, 'r') as f:
            content = f.read()
        
        if "sys.path.append" not in content:
            # Add import fix after the first line
            lines = content.split('\n')
            if lines[0].startswith('#'):
                # Keep the comment line
                new_content = lines[0] + '\n' + IMPORT_FIX + '\n'.join(lines[1:])
            else:
                new_content = IMPORT_FIX + content
            
            with open(file, 'w') as f:
                f.write(new_content)
            
            print(f"Fixed imports in {file}")

print("Import fixes applied!")