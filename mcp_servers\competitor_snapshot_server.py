# mcp_servers/competitor_snapshot_server.py
from mcp.server.fastmcp import FastMCP, Context
from services.sqlite_services import SQLiteService
from services.dataforseo_service import DataForSEOService
from typing import Dict, List
import httpx
from datetime import datetime
import re

mcp = FastMCP("SEO Competitor Snapshot")
nocodb = SQLiteService()
dataforseo = DataForSEOService()

@mcp.tool(description="Analyze competitor content for a keyword")
async def analyze_competitor_content(keyword: str, ctx: Context) -> Dict:
    """
    Get competitor analysis for a specific keyword
    """
    ctx.info(f"Analyzing competitors for: {keyword}")
    
    # Get SERP results from DataForSEO
    serp_results = await dataforseo.get_serp_results(keyword)
    
    # Extract top 5 organic results
    organic_results = [r for r in serp_results if r.get("type") == "organic"][:5]
    
    competitor_data = []
    
    for idx, result in enumerate(organic_results):
        ctx.report_progress(idx, len(organic_results))
        
        url = result.get("url", "")
        title = result.get("title", "")
        description = result.get("description", "")
        
        # Try to estimate word count (would need actual scraping in production)
        # For now, we'll use description length as a proxy
        estimated_words = len(description.split()) * 20  # Rough estimate
        
        competitor_data.append({
            "keyword": keyword,
            "competitor_url": url,
            "title": title,
            "word_count": estimated_words,
            "position": idx + 1,
            "scraped_at": datetime.now().isoformat()
        })
    
    # Save to competitor audit table
    if competitor_data:
        await nocodb.bulk_create("competitor_audit", competitor_data)
    
    return {
        "keyword": keyword,
        "competitors_analyzed": len(competitor_data),
        "top_competitor": competitor_data[0] if competitor_data else None
    }

@mcp.tool(description="Run competitor analysis for all pillar keywords")
async def batch_competitor_analysis(ctx: Context) -> Dict:
    """Analyze competitors for all pillar keywords in clusters"""
    # Get all clusters
    clusters = await nocodb.get_table_data("clusters")
    
    ctx.info(f"Analyzing competitors for {len(clusters)} pillar keywords")
    
    results = []
    for idx, cluster in enumerate(clusters):
        ctx.report_progress(idx, len(clusters))
        
        pillar = cluster.get("pillar_keyword", "")
        if pillar:
            result = await analyze_competitor_content(pillar, ctx)
            results.append(result)
    
    return {
        "total_analyzed": len(results),
        "keywords": [r["keyword"] for r in results]
    }

@mcp.tool(description="Get competitive gap analysis")
async def competitive_gap_analysis(cluster_id: str) -> Dict:
    """
    Analyze competitive gaps for a specific cluster
    """
    # Get cluster data
    clusters = await nocodb.get_table_data("clusters", {"id": cluster_id})
    if not clusters:
        return {"error": "Cluster not found"}
    
    cluster = clusters[0]
    pillar = cluster["pillar_keyword"]
    
    # Get competitor data for this keyword
    competitors = await nocodb.get_table_data("competitor_audit", {"keyword": pillar})
    
    if not competitors:
        return {"error": "No competitor data found. Run competitor analysis first."}
    
    # Calculate averages
    avg_word_count = sum(c.get("word_count", 0) for c in competitors) / len(competitors)
    
    # Get our brief if it exists
    our_brief = cluster.get("brief", "")
    our_estimated_words = len(our_brief.split()) if our_brief else 0
    
    gaps = []
    if our_estimated_words < avg_word_count * 0.8:
        gaps.append(f"Content length gap: Competitors average {avg_word_count:.0f} words")
    
    # Analyze title patterns
    title_keywords = {}
    for comp in competitors:
        title = comp.get("title", "").lower()
        for word in title.split():
            if len(word) > 3:  # Skip short words
                title_keywords[word] = title_keywords.get(word, 0) + 1
    
    common_title_words = sorted(title_keywords.items(), key=lambda x: x[1], reverse=True)[:5]
    
    return {
        "cluster_id": cluster_id,
        "pillar": pillar,
        "competitor_analysis": {
            "avg_word_count": avg_word_count,
            "our_word_count": our_estimated_words,
            "word_count_gap": avg_word_count - our_estimated_words,
            "common_title_patterns": [w[0] for w in common_title_words],
            "recommendations": gaps
        }
    }

@mcp.resource("competitors://summary")
async def get_competitor_summary() -> str:
    """Get summary of all competitor analysis"""
    all_competitors = await nocodb.get_table_data("competitor_audit")
    
    # Group by position
    position_counts = {}
    for comp in all_competitors:
        pos = comp.get("position", 0)
        position_counts[pos] = position_counts.get(pos, 0) + 1
    
    # Average word counts by position
    position_words = {}
    for pos in range(1, 6):
        pos_comps = [c for c in all_competitors if c.get("position") == pos]
        if pos_comps:
            position_words[pos] = sum(c.get("word_count", 0) for c in pos_comps) / len(pos_comps)
    
    return f"""
    Competitor Analysis Summary:
    Total URLs Analyzed: {len(all_competitors)}
    
    Average Word Count by Position:
    {'\n'.join(f'Position {pos}: {words:.0f} words' for pos, words in position_words.items())}
    
    Keywords Analyzed: {len(set(c.get("keyword", "") for c in all_competitors))}
    """

if __name__ == "__main__":
    mcp.run()

