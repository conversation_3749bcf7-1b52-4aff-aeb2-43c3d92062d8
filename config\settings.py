import os
from pathlib import Path
from typing import Optional
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

load_dotenv()

class Settings(BaseSettings):
    # NocoDB Configuration
    nocodb_api_url: str = os.getenv("NOCODB_API_URL", "http://localhost:8080")
    nocodb_api_token: str = os.getenv("NOCODB_API_TOKEN", "")
    nocodb_workspace_id: str = os.getenv("NOCODB_WORKSPACE_ID", "noco")
    nocodb_base_id: str = os.getenv("NOCODB_BASE_ID", "")
    
    # DataForSEO Configuration
    dataforseo_login: str = os.getenv("DATAFORSEO_LOGIN", "")
    dataforseo_password: str = os.getenv("DATAFORSEO_PASSWORD", "")
    dataforseo_base_url: str = "https://api.dataforseo.com/v3"

    # Proxy Configuration
    proxy_host: Optional[str] = os.getenv("PROXY_HOST")
    proxy_user: Optional[str] = os.getenv("PROXY_USER")
    proxy_pass: Optional[str] = os.getenv("PROXY_PASS")
    proxy_port: Optional[int] = os.getenv("PROXY_PORT")
    proxy_session_token: Optional[str] = os.getenv("PROXY_SESSION_TOKEN")
    
    # OpenAI Configuration
    openai_api_key: str = os.getenv("OPENAI_API_KEY", "")
    openai_model: str = "gpt-4-turbo-preview"
    
    # Notification Configuration
    slack_webhook_url: Optional[str] = os.getenv("SLACK_WEBHOOK_URL")
    smtp_host: str = os.getenv("SMTP_HOST", "smtp.gmail.com")
    smtp_port: int = int(os.getenv("SMTP_PORT", "587"))
    smtp_user: Optional[str] = os.getenv("SMTP_USER")
    smtp_password: Optional[str] = os.getenv("SMTP_PASSWORD")
    
    # SEO Configuration
    min_search_volume: int = 10
    max_keyword_difficulty: int = 60
    batch_size: int = 10
    
    class Config:
        env_file = ".env"
        case_sensitive = False

settings = Settings()
