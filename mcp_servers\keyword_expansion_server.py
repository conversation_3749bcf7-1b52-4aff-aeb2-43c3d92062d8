from mcp.server.fastmcp import FastMCP, Context
from services.nocodb_service import NocoDBService
from services.dataforseo_service import DataForSEOService
from config.settings import settings
from typing import Dict, List
import asyncio

mcp = FastMCP("SEO Keyword Expansion")
nocodb = NocoDBService()
dataforseo = DataForSEOService()

@mcp.tool(description="Expand a seed keyword into related keywords")
async def expand_keyword(seed_keyword_id: str, ctx: Context) -> Dict:
    """
    Expand a seed keyword using mock data (replace with DataForSEO in production)
    """
    # Mock seed keywords data
    mock_seeds = {
        "1": {"normalized_keyword": "seo tools", "raw_keyword": "seo tools"},
        "2": {"normalized_keyword": "best keyword research", "raw_keyword": "best keyword research"},
        "3": {"normalized_keyword": "content marketing strategy", "raw_keyword": "content marketing strategy"}
    }

    if seed_keyword_id not in mock_seeds:
        return {"error": f"Seed keyword not found. Available IDs: {list(mock_seeds.keys())}"}

    seed = mock_seeds[seed_keyword_id]
    keyword = seed.get("normalized_keyword", seed.get("raw_keyword", ""))

    ctx.info(f"Expanding keyword: {keyword}")

    # Mock related keywords based on the seed keyword
    mock_related_keywords = {
        "seo tools": [
            {"keyword": "best seo tools", "search_volume": 8100, "keyword_difficulty": 45, "cpc": 12.50},
            {"keyword": "free seo tools", "search_volume": 5400, "keyword_difficulty": 38, "cpc": 8.20},
            {"keyword": "seo tools 2024", "search_volume": 2900, "keyword_difficulty": 42, "cpc": 15.30},
            {"keyword": "seo analysis tools", "search_volume": 1900, "keyword_difficulty": 35, "cpc": 18.40},
            {"keyword": "seo audit tools", "search_volume": 1600, "keyword_difficulty": 40, "cpc": 22.10}
        ],
        "best keyword research": [
            {"keyword": "keyword research tools", "search_volume": 6600, "keyword_difficulty": 48, "cpc": 16.80},
            {"keyword": "free keyword research", "search_volume": 3200, "keyword_difficulty": 35, "cpc": 9.50},
            {"keyword": "keyword research guide", "search_volume": 1800, "keyword_difficulty": 32, "cpc": 11.20},
            {"keyword": "how to do keyword research", "search_volume": 2400, "keyword_difficulty": 28, "cpc": 7.90},
            {"keyword": "keyword research strategy", "search_volume": 1200, "keyword_difficulty": 38, "cpc": 14.60}
        ],
        "content marketing strategy": [
            {"keyword": "content marketing plan", "search_volume": 4100, "keyword_difficulty": 42, "cpc": 19.30},
            {"keyword": "content strategy framework", "search_volume": 1900, "keyword_difficulty": 45, "cpc": 25.40},
            {"keyword": "content marketing examples", "search_volume": 2800, "keyword_difficulty": 35, "cpc": 12.80},
            {"keyword": "content marketing tips", "search_volume": 3600, "keyword_difficulty": 38, "cpc": 15.70},
            {"keyword": "b2b content marketing", "search_volume": 2200, "keyword_difficulty": 48, "cpc": 28.90}
        ]
    }

    related = mock_related_keywords.get(keyword, [
        {"keyword": f"{keyword} guide", "search_volume": 1000, "keyword_difficulty": 30, "cpc": 10.00},
        {"keyword": f"best {keyword}", "search_volume": 800, "keyword_difficulty": 35, "cpc": 12.00},
        {"keyword": f"{keyword} tips", "search_volume": 600, "keyword_difficulty": 25, "cpc": 8.00}
    ])
    
    # Filter based on thresholds (using mock data structure)
    filtered_keywords = []
    for item in related:
        sv = item.get("search_volume", 0)
        kd = item.get("keyword_difficulty", 0)
        cpc = item.get("cpc", 0)

        # Apply basic filtering (using default thresholds for demo)
        min_volume = 10  # settings.min_search_volume
        max_difficulty = 50  # settings.max_keyword_difficulty

        if sv >= min_volume and kd <= max_difficulty:
            filtered_keywords.append({
                "seed_keyword_id": seed_keyword_id,
                "keyword": item.get("keyword", ""),
                "search_volume": sv,
                "keyword_difficulty": kd,
                "cpc": cpc
            })

    ctx.info(f"Found {len(filtered_keywords)} qualifying keywords")

    # Mock database operations for demo
    if filtered_keywords:
        ctx.info(f"Would save {len(filtered_keywords)} keywords to database")
        ctx.info(f"Would update seed keyword {seed_keyword_id} status to 'Expanded'")

    return {
        "seed_keyword": keyword,
        "seed_keyword_id": seed_keyword_id,
        "total_found": len(related),
        "qualified": len(filtered_keywords),
        "keywords": filtered_keywords,
        "filters": {
            "min_sv": 10,
            "max_kd": 50
        },
        "note": "This is demo data. Connect to real DataForSEO API and database for production use."
    }

@mcp.tool(description="Batch expand all ready keywords")
async def batch_expand_keywords(ctx: Context) -> Dict:
    """Expand all keywords marked as ready for expansion"""
    # Mock keywords ready for expansion
    ready_keywords = ["1", "2", "3"]

    ctx.info(f"Found {len(ready_keywords)} keywords ready for expansion")

    results = []
    for keyword_id in ready_keywords:
        ctx.report_progress(len(results), len(ready_keywords))
        result = await expand_keyword(keyword_id, ctx)
        results.append(result)
        await asyncio.sleep(0.5)  # Small delay for demo

    return {
        "total_processed": len(results),
        "results": results,
        "note": "Batch processed mock data. Connect to real database for production use."
    }

@mcp.resource("expanded-keywords://stats")
async def get_expansion_stats() -> str:
    """Get statistics on expanded keywords"""
    # Mock statistics for demo
    mock_stats = {
        "total": 15,
        "avg_search_volume": 3200,
        "avg_keyword_difficulty": 38.5,
        "top_keywords": [
            "best seo tools (8100 vol, 45 KD)",
            "keyword research tools (6600 vol, 48 KD)",
            "free seo tools (5400 vol, 38 KD)"
        ]
    }

    return f"""
    Expanded Keywords Statistics:
    - Total Keywords: {mock_stats['total']}
    - Average Search Volume: {mock_stats['avg_search_volume']:,}
    - Average Keyword Difficulty: {mock_stats['avg_keyword_difficulty']:.1f}

    Top Performing Keywords:
    """ + '\n'.join(f"  • {kw}" for kw in mock_stats['top_keywords']) + """

    Note: This is demo data. Connect to real database for production statistics.
    """

if __name__ == "__main__":
    mcp.run()