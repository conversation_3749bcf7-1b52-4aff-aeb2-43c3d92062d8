#!/usr/bin/env python3
"""
Test NocoDB connection and discover workspace/base IDs
"""
import asyncio
import httpx
import os
from dotenv import load_dotenv

load_dotenv()

async def test_nocodb_connection():
    """Test NocoDB connection and list available workspaces/bases"""
    
    api_url = os.getenv("NOCODB_API_URL", "https://app.nocodb.com")
    api_token = os.getenv("NOCODB_API_TOKEN", "")
    
    if not api_token:
        print("❌ NOCODB_API_TOKEN not found in .env file")
        return
    
    headers = {
        "xc-token": api_token,
        "Content-Type": "application/json"
    }
    
    print(f"🔗 Testing connection to: {api_url}")
    print(f"🔑 Using token: {api_token[:10]}...")
    
    async with httpx.AsyncClient() as client:
        try:
            # Test basic connection by getting user info
            print("\n📋 Testing basic connection...")
            response = await client.get(f"{api_url}/api/v1/auth/user/me", headers=headers)
            
            if response.status_code == 200:
                user_info = response.json()
                print(f"✅ Connected successfully!")
                print(f"   User: {user_info.get('email', 'Unknown')}")
            else:
                print(f"❌ Connection failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return
                
        except Exception as e:
            print(f"❌ Connection error: {e}")
            return
    
        try:
            # List workspaces
            print("\n📁 Listing workspaces...")
            response = await client.get(f"{api_url}/api/v1/workspaces", headers=headers)
            
            if response.status_code == 200:
                workspaces = response.json()
                print(f"✅ Found {len(workspaces)} workspace(s):")
                
                for ws in workspaces:
                    print(f"   - {ws.get('title', 'Untitled')} (ID: {ws.get('id')})")
                    
                    # List bases in this workspace
                    ws_id = ws.get('id')
                    if ws_id:
                        try:
                            bases_response = await client.get(
                                f"{api_url}/api/v1/db/meta/projects",
                                headers=headers,
                                params={"workspace_id": ws_id}
                            )
                            
                            if bases_response.status_code == 200:
                                bases = bases_response.json()
                                print(f"     📊 Bases in this workspace:")
                                for base in bases:
                                    print(f"       - {base.get('title', 'Untitled')} (ID: {base.get('id')})")
                            
                        except Exception as e:
                            print(f"     ⚠️ Could not list bases: {e}")
            else:
                print(f"❌ Could not list workspaces: {response.status_code}")
                print(f"   Response: {response.text}")
                
        except Exception as e:
            print(f"❌ Error listing workspaces: {e}")
    
        # Try alternative API endpoints for hosted NocoDB
        print("\n🔍 Trying alternative endpoints...")
        
        try:
            # Try to list projects directly
            response = await client.get(f"{api_url}/api/v1/db/meta/projects", headers=headers)
            
            if response.status_code == 200:
                projects = response.json()
                print(f"✅ Found {len(projects)} project(s) via direct API:")
                
                for project in projects:
                    project_id = project.get('id')
                    project_title = project.get('title', 'Untitled')
                    print(f"   - {project_title} (ID: {project_id})")
                    
                    # Try to list tables in this project
                    if project_id:
                        try:
                            tables_response = await client.get(
                                f"{api_url}/api/v1/db/meta/projects/{project_id}/tables",
                                headers=headers
                            )
                            
                            if tables_response.status_code == 200:
                                tables = tables_response.json()
                                print(f"     📋 Tables in this project:")
                                for table in tables:
                                    print(f"       - {table.get('title', 'Untitled')}")
                                    
                                # Test data access for first table
                                if tables:
                                    first_table = tables[0].get('title')
                                    print(f"\n🧪 Testing data access for table '{first_table}'...")
                                    
                                    # Try different URL patterns
                                    test_urls = [
                                        f"{api_url}/api/v1/db/data/noco/{first_table}",
                                        f"{api_url}/api/v1/db/data/{project_id}/{first_table}",
                                        f"{api_url}/api/v1/db/data/v1/{project_id}/{first_table}",
                                    ]
                                    
                                    for test_url in test_urls:
                                        try:
                                            test_response = await client.get(test_url, headers=headers)
                                            print(f"     {test_url}: {test_response.status_code}")
                                            if test_response.status_code == 200:
                                                print(f"     ✅ This URL pattern works!")
                                                break
                                        except Exception as e:
                                            print(f"     {test_url}: Error - {e}")
                            
                        except Exception as e:
                            print(f"     ⚠️ Could not list tables: {e}")
            else:
                print(f"❌ Could not list projects: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error with alternative endpoints: {e}")

if __name__ == "__main__":
    asyncio.run(test_nocodb_connection())
