from mcp.server.fastmcp import FastMCP, Context
from typing import Dict, List

mcp = FastMCP("SEO Intent & Funnel Classifier")

@mcp.tool(description="Classify intent and funnel stage for keywords")
async def classify_keyword_batch(keyword_ids: List[str], ctx: Context) -> Dict:
    """
    Classify search intent and funnel stage for a batch of keywords (mock version)
    """
    # Mock expanded keywords data
    mock_keywords = {
        "1": {"id": "1", "keyword": "best seo tools", "search_volume": 8100},
        "2": {"id": "2", "keyword": "free seo tools", "search_volume": 5400},
        "3": {"id": "3", "keyword": "seo tools 2024", "search_volume": 2900},
        "4": {"id": "4", "keyword": "keyword research tools", "search_volume": 6600},
        "5": {"id": "5", "keyword": "how to do keyword research", "search_volume": 2400},
        "6": {"id": "6", "keyword": "content marketing plan", "search_volume": 4100},
        "7": {"id": "7", "keyword": "buy seo software", "search_volume": 1200},
        "8": {"id": "8", "keyword": "seo tools pricing", "search_volume": 800}
    }

    # Filter keywords based on provided IDs
    keywords_to_process = []
    for keyword_id in keyword_ids:
        if keyword_id in mock_keywords:
            keywords_to_process.append(mock_keywords[keyword_id])

    if not keywords_to_process:
        return {
            "error": f"No keywords found for IDs: {keyword_ids}",
            "available_ids": list(mock_keywords.keys())
        }

    ctx.info(f"Classifying {len(keywords_to_process)} keywords")

    # Mock classification logic
    results = []
    for keyword_record in keywords_to_process:
        keyword = keyword_record["keyword"].lower()

        # Simple intent classification based on keyword patterns
        if any(word in keyword for word in ["buy", "price", "pricing", "cost", "purchase"]):
            intent = "transactional"
            funnel_stage = "decision"
        elif any(word in keyword for word in ["best", "top", "review", "compare", "vs"]):
            intent = "commercial"
            funnel_stage = "consideration"
        elif any(word in keyword for word in ["how", "what", "why", "guide", "tutorial"]):
            intent = "informational"
            funnel_stage = "awareness"
        else:
            intent = "informational"
            funnel_stage = "awareness"

        result = {
            "keyword_id": keyword_record["id"],
            "keyword": keyword_record["keyword"],
            "intent": intent,
            "funnel_stage": funnel_stage,
            "search_volume": keyword_record.get("search_volume", 0)
        }
        results.append(result)

        ctx.info(f"Classified '{keyword_record['keyword']}' as {intent} ({funnel_stage})")

    # Mock database update
    ctx.info(f"Would update {len(results)} records in database")

    return {
        "processed": len(keywords_to_process),
        "classifications": results[:5]  # Sample of results
    }

@mcp.tool(description="Auto-classify all unclassified keywords")
async def auto_classify_keywords(ctx: Context) -> Dict:
    """Automatically classify all keywords without intent/funnel data (mock version)"""
    # Mock: assume all keywords need classification
    all_keyword_ids = ["1", "2", "3", "4", "5", "6", "7", "8"]

    ctx.info(f"Found {len(all_keyword_ids)} keywords to classify")

    # Process all keywords
    result = await classify_keyword_batch(all_keyword_ids, ctx)

    return {
        "total_batches": 1,
        "total_processed": result.get("processed", 0),
        "sample_classifications": result.get("classifications", [])
    }

@mcp.resource("intent-distribution://current")
async def get_intent_distribution() -> str:
    """Get the current distribution of search intents (mock data)"""
    # Mock distribution data
    intent_counts = {
        "informational": 4,
        "commercial": 2,
        "transactional": 2
    }

    funnel_counts = {
        "awareness": 4,
        "consideration": 2,
        "decision": 2
    }

    total_keywords = sum(intent_counts.values())

    return f"""
    Intent Distribution:
    """ + '\n'.join(f'- {intent}: {count} ({count/total_keywords*100:.1f}%)' for intent, count in intent_counts.items()) + """

    Funnel Stage Distribution:
    """ + '\n'.join(f'- {stage}: {count} ({count/total_keywords*100:.1f}%)' for stage, count in funnel_counts.items()) + """
    """

if __name__ == "__main__":
    mcp.run()