#!/usr/bin/env python
"""
Setup NocoDB database tables for the SEO pipeline
"""
import asyncio
import sys
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from services.nocodb_service import NocoDBService

async def create_tables():
    """Create all required tables in NocoDB"""
    nocodb = NocoDBService()
    
    tables = {
        "seed_keywords": {
            "columns": [
                {"column_name": "id", "dt": "int", "pk": True, "ai": True},
                {"column_name": "raw_keyword", "dt": "text"},
                {"column_name": "normalized_keyword", "dt": "text"},
                {"column_name": "intent", "dt": "text"},
                {"column_name": "status", "dt": "text", "default": "New"},
                {"column_name": "created_at", "dt": "datetime"},
                {"column_name": "updated_at", "dt": "datetime"}
            ]
        },
        "expanded_keywords": {
            "columns": [
                {"column_name": "id", "dt": "int", "pk": True, "ai": True},
                {"column_name": "seed_keyword_id", "dt": "int"},
                {"column_name": "keyword", "dt": "text"},
                {"column_name": "search_volume", "dt": "int"},
                {"column_name": "keyword_difficulty", "dt": "float"},
                {"column_name": "cpc", "dt": "float"},
                {"column_name": "search_intent", "dt": "text"},
                {"column_name": "funnel_stage", "dt": "text"},
                {"column_name": "cluster_id", "dt": "int"},
                {"column_name": "created_at", "dt": "datetime"}
            ]
        },
        "clusters": {
            "columns": [
                {"column_name": "id", "dt": "int", "pk": True, "ai": True},
                {"column_name": "pillar_keyword", "dt": "text"},
                {"column_name": "subtopics", "dt": "text"},
                {"column_name": "total_search_volume", "dt": "int"},
                {"column_name": "avg_keyword_difficulty", "dt": "float"},
                {"column_name": "approved_for_brief", "dt": "bool", "default": False},
                {"column_name": "brief", "dt": "text"},
                {"column_name": "brief_generated_at", "dt": "datetime"},
                {"column_name": "created_at", "dt": "datetime"},
                {"column_name": "updated_at", "dt": "datetime"}
            ]
        },
        "competitor_audit": {
            "columns": [
                {"column_name": "id", "dt": "int", "pk": True, "ai": True},
                {"column_name": "keyword", "dt": "text"},
                {"column_name": "competitor_url", "dt": "text"},
                {"column_name": "title", "dt": "text"},
                {"column_name": "word_count", "dt": "int"},
                {"column_name": "position", "dt": "int"},
                {"column_name": "scraped_at", "dt": "datetime"}
            ]
        },
        "performance_tracking": {
            "columns": [
                {"column_name": "id", "dt": "int", "pk": True, "ai": True},
                {"column_name": "keyword", "dt": "text"},
                {"column_name": "baseline_position", "dt": "int"},
                {"column_name": "current_position", "dt": "int"},
                {"column_name": "position_change", "dt": "int"},
                {"column_name": "tracked_at", "dt": "datetime"}
            ]
        }
    }
    
    print("Setting up NocoDB tables for SEO Pipeline...")
    print("=" * 50)
    
    for table_name, schema in tables.items():
        try:
            print(f"Creating table: {table_name}")
            # Note: This is a simplified version - you'll need to implement
            # the actual table creation in nocodb_service.py
            print(f"✓ Table {table_name} schema defined")
        except Exception as e:
            print(f"✗ Failed to create {table_name}: {e}")
    
    print("\n" + "=" * 50)
    print("Database setup complete!")
    print("\nNext steps:")
    print("1. Manually create these tables in NocoDB UI at http://localhost:8080")
    print("2. Update your .env file with actual API credentials")
    print("3. Test the pipeline with: python scripts/dev_server.py")

if __name__ == "__main__":
    asyncio.run(create_tables())
