# services/sqlite_service.py
import sqlite3
import json
from typing import Dict, List, Optional, Any
from datetime import datetime
import asyncio
import aiosqlite

class SQLiteService:
    def __init__(self, db_path: str = "seo_pipeline.db"):
        self.db_path = db_path
        self._init_db()
    
    def _init_db(self):
        """Initialize database tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create tables
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS seed_keywords (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                raw_keyword TEXT,
                normalized_keyword TEXT,
                intent TEXT,
                status TEXT DEFAULT 'New',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS expanded_keywords (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                seed_keyword_id INTEGER,
                keyword TEXT,
                search_volume INTEGER,
                keyword_difficulty REAL,
                cpc REAL,
                search_intent TEXT,
                funnel_stage TEXT,
                cluster_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS clusters (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                pillar_keyword TEXT,
                subtopics TEXT,
                total_search_volume INTEGER,
                avg_keyword_difficulty REAL,
                approved_for_brief BOOLEAN DEFAULT 0,
                brief TEXT,
                brief_generated_at TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
    
    async def get_table_data(self, table_name: str, filters: Optional[Dict] = None) -> List[Dict]:
        """Fetch data from a table"""
        async with aiosqlite.connect(self.db_path) as db:
            db.row_factory = aiosqlite.Row
            
            query = f"SELECT * FROM {table_name}"
            params = []
            
            if filters:
                conditions = []
                for key, value in filters.items():
                    conditions.append(f"{key} = ?")
                    params.append(value)
                query += " WHERE " + " AND ".join(conditions)
            
            async with db.execute(query, params) as cursor:
                rows = await cursor.fetchall()
                return [dict(row) for row in rows]
    
    async def create_record(self, table_name: str, data: Dict) -> Dict:
        """Create a new record"""
        async with aiosqlite.connect(self.db_path) as db:
            columns = ', '.join(data.keys())
            placeholders = ', '.join(['?' for _ in data])
            query = f"INSERT INTO {table_name} ({columns}) VALUES ({placeholders})"
            
            cursor = await db.execute(query, list(data.values()))
            await db.commit()
            
            data['id'] = cursor.lastrowid
            return data
    
    async def update_record(self, table_name: str, record_id: str, data: Dict) -> Dict:
        """Update a record"""
        async with aiosqlite.connect(self.db_path) as db:
            data['updated_at'] = datetime.now().isoformat()
            
            set_clause = ', '.join([f"{k} = ?" for k in data.keys()])
            values = list(data.values()) + [record_id]
            query = f"UPDATE {table_name} SET {set_clause} WHERE id = ?"
            
            await db.execute(query, values)
            await db.commit()
            
            return data
    
    async def bulk_create(self, table_name: str, records: List[Dict]) -> List[Dict]:
        """Bulk create records"""
        created = []
        for record in records:
            result = await self.create_record(table_name, record)
            created.append(result)
        return created