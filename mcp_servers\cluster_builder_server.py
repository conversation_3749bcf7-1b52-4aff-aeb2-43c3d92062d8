# mcp_servers/cluster_builder_server.py
from mcp.server.fastmcp import FastMCP, Context
from services.sqlite_services import SQLiteService
from services.openai_service import OpenAIService
from typing import Dict, List
import json
from datetime import datetime

mcp = FastMCP("SEO Keyword Cluster Builder")
nocodb = SQLiteService()
openai_service = OpenAIService()

@mcp.tool(description="Build keyword clusters from expanded keywords")
async def build_keyword_clusters(ctx: Context) -> Dict:
    """
    Analyze all expanded keywords and create topical clusters
    """
    # Fetch all expanded keywords with metrics
    keywords = await nocodb.get_table_data("expanded_keywords")
    
    if not keywords:
        return {"error": "No expanded keywords found"}
    
    ctx.info(f"Building clusters from {len(keywords)} keywords")
    
    # Prepare keyword data for clustering
    keyword_data = []
    for kw in keywords:
        keyword_data.append({
            "id": kw["id"],
            "keyword": kw["keyword"],
            "search_volume": kw.get("search_volume", 0),
            "keyword_difficulty": kw.get("keyword_difficulty", 0),
            "intent": kw.get("search_intent", "informational"),
            "funnel_stage": kw.get("funnel_stage", "awareness")
        })
    
    # Use OpenAI to cluster keywords
    clusters = await openai_service.cluster_keywords(keyword_data)
    
    ctx.info(f"Created {len(clusters)} clusters")
    
    # Save clusters to NocoDB
    cluster_records = []
    for cluster in clusters:
        cluster_record = {
            "pillar_keyword": cluster["pillar_keyword"],
            "subtopics": json.dumps(cluster["subtopics"]),
            "total_search_volume": cluster["total_search_volume"],
            "avg_keyword_difficulty": cluster["avg_keyword_difficulty"],
            "approved_for_brief": False,
            "created_at": datetime.now().isoformat()
        }
        cluster_records.append(cluster_record)
    
    created_clusters = await nocodb.bulk_create("clusters", cluster_records)
    
    # Update keywords with cluster assignments
    for cluster_idx, cluster in enumerate(clusters):
        cluster_id = created_clusters[cluster_idx]["id"] if cluster_idx < len(created_clusters) else None
        if cluster_id:
            for subtopic in cluster["subtopics"]:
                # Find keywords matching this subtopic
                matching_kws = [k for k in keyword_data if k["keyword"] == subtopic]
                for kw in matching_kws:
                    await nocodb.update_record("expanded_keywords", str(kw["id"]), {"cluster_id": cluster_id})
    
    return {
        "clusters_created": len(clusters),
        "clusters": [{
            "pillar": c["pillar_keyword"],
            "subtopics_count": len(c["subtopics"]),
            "total_sv": c["total_search_volume"]
        } for c in clusters[:5]]  # Return sample
    }

@mcp.tool(description="Analyze cluster potential")
async def analyze_cluster_potential(cluster_id: str) -> Dict:
    """
    Analyze the potential of a specific keyword cluster
    """
    clusters = await nocodb.get_table_data("clusters", {"id": cluster_id})
    if not clusters:
        return {"error": "Cluster not found"}
    
    cluster = clusters[0]
    subtopics = json.loads(cluster.get("subtopics", "[]"))
    
    # Get all keywords in this cluster
    cluster_keywords = await nocodb.get_table_data("expanded_keywords", {"cluster_id": cluster_id})
    
    # Calculate metrics
    intent_distribution = {}
    funnel_distribution = {}
    total_cpc_value = 0
    
    for kw in cluster_keywords:
        intent = kw.get("search_intent", "unknown")
        funnel = kw.get("funnel_stage", "unknown")
        
        intent_distribution[intent] = intent_distribution.get(intent, 0) + 1
        funnel_distribution[funnel] = funnel_distribution.get(funnel, 0) + 1
        total_cpc_value += kw.get("cpc", 0) * kw.get("search_volume", 0)
    
    return {
        "cluster_id": cluster_id,
        "pillar": cluster["pillar_keyword"],
        "metrics": {
            "total_keywords": len(cluster_keywords),
            "total_search_volume": cluster["total_search_volume"],
            "avg_difficulty": cluster["avg_keyword_difficulty"],
            "monthly_cpc_value": total_cpc_value,
            "intent_distribution": intent_distribution,
            "funnel_distribution": funnel_distribution
        },
        "recommendation": "High potential" if total_cpc_value > 1000 and cluster["avg_keyword_difficulty"] < 50 else "Medium potential"
    }

@mcp.resource("clusters://top-opportunities")
async def get_top_cluster_opportunities() -> str:
    """Get the top cluster opportunities based on volume and difficulty"""
    clusters = await nocodb.get_table_data("clusters")
    
    # Sort by opportunity score (high volume, low difficulty)
    sorted_clusters = sorted(
        clusters,
        key=lambda c: c.get("total_search_volume", 0) / (c.get("avg_keyword_difficulty", 1) + 1),
        reverse=True
    )
    
    top_5 = sorted_clusters[:5]
    
    cluster_list = '\n'.join(f'{i+1}. {c["pillar_keyword"]} - SV: {c["total_search_volume"]:,}, KD: {c["avg_keyword_difficulty"]:.1f}'
                                 for i, c in enumerate(top_5))

    return f"""
    Top 5 Cluster Opportunities:
    {cluster_list}
    """

if __name__ == "__main__":
    mcp.run()

# mcp_servers/content_brief_server.py
from mcp.server.fastmcp import FastMCP, Context
from services.nocodb_service import NocoDBService
from services.openai_service import OpenAIService
from services.notification_service import NotificationService
from typing import Dict
import json
from datetime import datetime

mcp = FastMCP("SEO Content Brief Generator")
nocodb = NocoDBService()
openai_service = OpenAIService()
notifications = NotificationService()

@mcp.tool(description="Generate a content brief for an approved cluster")
async def generate_content_brief(cluster_id: str, ctx: Context) -> Dict:
    """
    Generate a comprehensive content brief for a keyword cluster
    """
    # Get cluster data
    clusters = await nocodb.get_table_data("clusters", {"id": cluster_id})
    if not clusters:
        return {"error": "Cluster not found"}
    
    cluster = clusters[0]
    
    if not cluster.get("approved_for_brief"):
        return {"error": "Cluster not approved for brief generation"}
    
    ctx.info(f"Generating brief for: {cluster['pillar_keyword']}")
    
    # Parse subtopics
    subtopics = json.loads(cluster.get("subtopics", "[]"))
    
    # Generate the brief using OpenAI
    brief_content = await openai_service.generate_content_brief(
        cluster["pillar_keyword"],
        subtopics
    )
    
    # Update cluster with brief
    await nocodb.update_record("clusters", cluster_id, {
        "brief": brief_content,
        "brief_generated_at": datetime.now().isoformat()
    })
    
    # Send notifications
    await notifications.send_slack_message(
        f"📝 New content brief generated for: {cluster['pillar_keyword']}\n"
        f"Total Search Volume: {cluster['total_search_volume']:,}\n"
        f"View in NocoDB: {nocodb.base_url}/dashboard/clusters/{cluster_id}"
    )
    
    return {
        "cluster_id": cluster_id,
        "pillar": cluster["pillar_keyword"],
        "brief_preview": brief_content[:500] + "...",
        "notification_sent": True
    }

@mcp.tool(description="Batch generate briefs for all approved clusters")
async def batch_generate_briefs(ctx: Context) -> Dict:
    """Generate briefs for all approved clusters without briefs"""
    # Get approved clusters without briefs
    clusters = await nocodb.get_table_data("clusters", {"approved_for_brief": True})
    clusters_needing_briefs = [c for c in clusters if not c.get("brief")]
    
    ctx.info(f"Found {len(clusters_needing_briefs)} clusters needing briefs")
    
    results = []
    for idx, cluster in enumerate(clusters_needing_briefs):
        ctx.report_progress(idx, len(clusters_needing_briefs))
        result = await generate_content_brief(str(cluster["id"]), ctx)
        results.append(result)
    
    # Send summary notification
    if results:
        await notifications.send_email(
            to=["<EMAIL>"],
            subject=f"SEO Pipeline: {len(results)} New Content Briefs Generated",
            body=f"""
            The SEO pipeline has generated {len(results)} new content briefs.

            Clusters processed:
            """ + '\n'.join(f"- {r['pillar']}" for r in results if 'pillar' in r) + f"""

            Access the briefs in NocoDB: {nocodb.base_url}/dashboard/clusters
            """
        )
    
    return {
        "total_generated": len(results),
        "clusters": [r.get("pillar", "Unknown") for r in results]
    }

@mcp.tool(description="Approve a cluster for brief generation")
async def approve_cluster_for_brief(cluster_id: str, notes: str = "") -> Dict:
    """Mark a cluster as approved for content brief generation"""
    clusters = await nocodb.get_table_data("clusters", {"id": cluster_id})
    if not clusters:
        return {"error": "Cluster not found"}
    
    await nocodb.update_record("clusters", cluster_id, {
        "approved_for_brief": True,
        "approval_notes": notes,
        "approved_at": datetime.now().isoformat()
    })
    
    return {
        "cluster_id": cluster_id,
        "status": "Approved for brief generation",
        "notes": notes
    }

@mcp.resource("briefs://pending-approval")
async def get_clusters_pending_approval() -> str:
    """Get clusters that haven't been approved for briefs yet"""
    clusters = await nocodb.get_table_data("clusters", {"approved_for_brief": False})
    
    high_potential = [c for c in clusters if c.get("total_search_volume", 0) > 5000 and c.get("avg_keyword_difficulty", 100) < 50]
    
    return f"""
    Clusters Pending Brief Approval:
    Total: {len(clusters)}
    High Potential (SV > 5000, KD < 50): {len(high_potential)}

    Top candidates:
    """ + '\n'.join(f"- {c['pillar_keyword']} (SV: {c['total_search_volume']:,}, KD: {c['avg_keyword_difficulty']:.1f})"
                     for c in high_potential[:10]) + """
    """

if __name__ == "__main__":
    mcp.run()
