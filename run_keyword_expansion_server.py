# run_keyword_expansion_server.py
import sys
import os

# Add the project root to Python path FIRST
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# Import and run the keyword expansion server
from mcp_servers.keyword_expansion_server import mcp

if __name__ == "__main__":
    print("Starting Keyword Expansion Server...")
    print("Use MCP Inspector to connect and test")
    mcp.run()
