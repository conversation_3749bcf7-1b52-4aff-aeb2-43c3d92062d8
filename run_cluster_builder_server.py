# run_cluster_builder_server.py
import sys
import os

# Add the project root to Python path FIRST
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# Import and run the cluster builder server
from mcp_servers.cluster_builder_server import mcp

if __name__ == "__main__":
    print("Starting Cluster Builder Server...")
    print("Use MCP Inspector to connect and test")
    mcp.run()
