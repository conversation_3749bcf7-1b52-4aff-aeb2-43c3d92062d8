# models/cluster.py
from pydantic import BaseModel, <PERSON>
from typing import List, Optional
from datetime import datetime

class KeywordCluster(BaseModel):
    id: Optional[int] = None
    pillar_keyword: str
    subtopics: List[str]
    total_search_volume: int = 0
    avg_keyword_difficulty: float = 0
    approved_for_brief: bool = False
    brief: Optional[str] = None
    brief_generated_at: Optional[datetime] = None
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: Optional[datetime] = None

# Alias for backward compatibility
Cluster = KeywordCluster

