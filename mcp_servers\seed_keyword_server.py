# mcp_servers/seed_keyword_server.py
from mcp.server.fastmcp import FastMC<PERSON>

# ADD THESE THREE LINES HERE:
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

# Now your existing imports will work:
from services.sqlite_services import SQLiteService
from services.openai_service import OpenAIService
from typing import Dict
import asyncio


mcp = FastMCP("SEO Seed Keyword Processor")
nocodb = SQLiteService()
openai_service = OpenAIService()

@mcp.tool(description="Process a new seed keyword")
async def process_seed_keyword(keyword_id: str) -> Dict:
    """
    Process a seed keyword: normalize it and classify its intent
    """
    # Fetch the seed keyword from NocoDB
    records = await nocodb.get_table_data("seed_keywords", {"id": keyword_id})
    if not records:
        return {"error": "Keyword not found"}
    
    record = records[0]
    raw_keyword = record.get("raw_keyword", "")
    
    # Normalize the keyword
    normalized = await openai_service.normalize_keyword(raw_keyword)
    
    # Classify intent
    intent_data = await openai_service.classify_intent([normalized])
    intent = intent_data[0].get("intent", "informational") if intent_data else "informational"
    
    # Update the record
    update_data = {
        "normalized_keyword": normalized,
        "intent": intent,
        "status": "Ready for Expansion"
    }
    
    await nocodb.update_record("seed_keywords", keyword_id, update_data)
    
    return {
        "keyword_id": keyword_id,
        "normalized": normalized,
        "intent": intent,
        "status": "Ready for Expansion"
    }

@mcp.resource("seed-keywords://pending")
async def get_pending_seeds() -> str:
    """Get all pending seed keywords"""
    records = await nocodb.get_table_data("seed_keywords", {"status": "New"})
    return f"Found {len(records)} pending seed keywords"

@mcp.tool(description="Batch process all pending seed keywords")
async def batch_process_seeds() -> Dict:
    """Process all pending seed keywords in batch"""
    records = await nocodb.get_table_data("seed_keywords", {"status": "New"})
    
    results = []
    for record in records:
        result = await process_seed_keyword(str(record["id"]))
        results.append(result)
        await asyncio.sleep(0.5)  # Rate limiting
    
    return {
        "processed": len(results),
        "results": results
    }

if __name__ == "__main__":
    mcp.run()