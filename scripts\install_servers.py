# scripts/install_servers.py
#!/usr/bin/env python
"""
Install all MCP servers for the SEO pipeline
"""
import subprocess
import sys
from pathlib import Path

SERVERS = [
    "mcp_servers/seed_keyword_server.py",
    "mcp_servers/keyword_expansion_server.py", 
    "mcp_servers/intent_classifier_server.py",
    "mcp_servers/cluster_builder_server.py",
    "mcp_servers/content_brief_server.py",
    "mcp_servers/competitor_snapshot_server.py",
    "mcp_servers/performance_tracker_server.py"
]

def install_server(server_path: str):
    """Install a single MCP server"""
    print(f"Installing {server_path}...")
    
    server_name = Path(server_path).stem.replace("_", "-")
    
    try:
        # Use the virtual environment's MCP CLI
        mcp_path = Path(__file__).parent.parent / "myenv" / "Scripts" / "mcp.exe"
        result = subprocess.run(
            [str(mcp_path), "install", server_path, "--name", f"seo-{server_name}"],
            capture_output=True,
            text=True,
            check=True
        )
        print(f"✓ Successfully installed {server_name}")
        print(result.stdout)
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install {server_name}")
        print(e.stderr)
        return False
    
    return True

def main():
    """Install all servers"""
    print("Installing SEO Pipeline MCP Servers")
    print("=" * 40)
    
    success_count = 0
    
    for server in SERVERS:
        if install_server(server):
            success_count += 1
        print()
    
    print(f"\nInstallation complete: {success_count}/{len(SERVERS)} servers installed")
    
    if success_count == len(SERVERS):
        print("\n✓ All servers installed successfully!")
        print("\nYou can now use these servers in Claude Desktop or test them with:")
        print("  mcp dev <server_file>")
    else:
        print("\n⚠ Some servers failed to install. Check the errors above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
