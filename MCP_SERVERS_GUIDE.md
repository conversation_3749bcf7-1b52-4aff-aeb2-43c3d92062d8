# 🚀 MCP Servers Complete Guide

## 📋 Table of Contents
- [What are MCP Servers?](#what-are-mcp-servers)
- [Available Servers](#available-servers)
- [How to Run Servers](#how-to-run-servers)
- [Server Details & Tools](#server-details--tools)
- [Input Formats](#input-formats)
- [Troubleshooting](#troubleshooting)

---

## 🤖 What are MCP Servers?

**MCP (Model Context Protocol) Servers** are specialized programs that expose tools and data to AI applications like Claude Desktop. Think of them as **API endpoints** but specifically designed for AI interactions.

### Why Use MCP Servers?
- ✅ **Modular**: Each server handles one specific task
- ✅ **Secure**: Controlled access to your data and tools
- ✅ **Scalable**: Run multiple servers for different purposes
- ✅ **Standardized**: Works with any MCP-compatible AI client

---

## 🏗️ Available Servers

| Server | Purpose | Status |
|--------|---------|--------|
| **Seed Keyword Server** | Process initial keywords | ✅ Ready |
| **Keyword Expansion Server** | Expand keywords into variations | ✅ Ready |
| **Intent Classifier Server** | Classify search intent | ✅ Ready |
| **Cluster Builder Server** | Group related keywords | ✅ Ready |
| **Content Brief Server** | Generate content briefs | ✅ Ready |
| **Competitor Snapshot Server** | Analyze competitors | ✅ Ready |
| **Performance Tracker Server** | Track keyword rankings | ✅ Ready |
| **Development Server** | All-in-one testing | ✅ Ready |

---

## 🚀 How to Run Servers

### Method 1: Individual Servers
```bash
# Terminal 1 - Seed Keywords
python run_seed_server.py

# Terminal 2 - Keyword Expansion  
python run_keyword_expansion_server.py

# Terminal 3 - Intent Classification
python run_intent_classifier_server.py

# Terminal 4 - Cluster Building
python run_cluster_builder_server.py
```

### Method 2: All-in-One Development Server
```bash
# Single terminal - All tools
python run_dev_server.py
```

### Method 3: Using MCP Inspector
1. **Open**: `localhost:3000` in browser
2. **Set Transport**: `STDIO`
3. **Set Command**: `python`
4. **Set Arguments**: `run_seed_server.py` (or any server)
5. **Click**: Connect

---

## 🔧 Server Details & Tools

### 1. 🌱 Seed Keyword Server
**Purpose**: Process and normalize initial keywords

#### Tools:
- **`process_seed_keyword`**
  - **Input**: `1` (keyword ID)
  - **What it does**: Takes raw keyword → normalizes → classifies intent
  - **Output**: Processed keyword ready for expansion
  - **Why**: Clean data before processing

- **`batch_process_seeds`**
  - **Input**: (empty)
  - **What it does**: Processes all pending keywords at once
  - **Output**: Summary of all processed keywords
  - **Why**: Bulk processing saves time

#### Example Input/Output:
```
Input: 1
Output: {
  "keyword_id": "1",
  "raw_keyword": "seo tools",
  "normalized": "seo tools",
  "intent": "informational",
  "status": "Ready for Expansion"
}
```

### 2. 🔍 Keyword Expansion Server
**Purpose**: Expand seed keywords into related variations

#### Tools:
- **`expand_keyword`**
  - **Input**: `1` (seed keyword ID)
  - **What it does**: Finds related keywords people actually search for
  - **Output**: List of expanded keywords with search data
  - **Why**: More keywords = more traffic opportunities

- **`batch_expand_keywords`**
  - **Input**: (empty)
  - **What it does**: Expands all processed seed keywords
  - **Output**: Complete keyword expansion results
  - **Why**: Bulk expansion for efficiency

#### Example Input/Output:
```
Input: 1
Output: {
  "seed_keyword": "seo tools",
  "expanded_keywords": [
    {"keyword": "best seo tools", "search_volume": 8100, "difficulty": 65},
    {"keyword": "free seo tools", "search_volume": 5400, "difficulty": 45}
  ],
  "total_keywords": 25
}
```

### 3. 🎯 Intent Classifier Server
**Purpose**: Classify search intent for better targeting

#### Tools:
- **`classify_intent`**
  - **Input**: `1` (keyword ID)
  - **What it does**: Determines if keyword is informational/commercial/transactional
  - **Output**: Intent classification with confidence score
  - **Why**: Different intents need different content strategies

#### Intent Types:
- **Informational**: "how to use seo tools" → Blog posts, guides
- **Commercial**: "best seo tools" → Comparison pages, reviews  
- **Transactional**: "buy seo tools" → Product pages, pricing

### 4. 🗂️ Cluster Builder Server
**Purpose**: Group related keywords into content clusters

#### Tools:
- **`build_clusters`**
  - **Input**: (empty)
  - **What it does**: Groups similar keywords together
  - **Output**: Keyword clusters with pillar keywords
  - **Why**: One piece of content can target multiple related keywords

#### Example Output:
```
Cluster 1: "SEO Tools" (Pillar)
├── best seo tools
├── free seo tools  
├── seo audit tools
└── seo keyword tools
```

### 5. 📝 Content Brief Server
**Purpose**: Generate AI-powered content briefs

#### Tools:
- **`generate_brief`**
  - **Input**: `1` (cluster ID)
  - **What it does**: Creates detailed content brief for a keyword cluster
  - **Output**: Complete content strategy document
  - **Why**: Structured briefs = better content = better rankings

### 6. 🕵️ Competitor Snapshot Server
**Purpose**: Analyze competitor content and strategies

#### Tools:
- **`analyze_competitors`**
  - **Input**: `seo tools` (keyword)
  - **What it does**: Finds top-ranking competitors and analyzes their content
  - **Output**: Competitor analysis with content gaps
  - **Why**: Know what you're competing against

### 7. 📊 Performance Tracker Server
**Purpose**: Track keyword rankings and performance

#### Tools:
- **`track_rankings`**
  - **Input**: (empty)
  - **What it does**: Checks current rankings for tracked keywords
  - **Output**: Ranking positions and changes
  - **Why**: Monitor SEO progress over time

### 8. 🛠️ Development Server
**Purpose**: All-in-one testing environment

#### Tools:
- **`test_nocodb`**: Test database connection
- **`test_dataforseo`**: Test SEO data API
- **`test_openai`**: Test AI service
- **`test_full_pipeline`**: Run complete workflow test

---

## 📝 Input Formats

### ✅ Correct Input Formats:
```
For keyword IDs: 1
For keywords: seo tools
For empty params: (leave blank)
```

### ❌ Wrong Input Formats:
```
{"keyword_id": "1"}  ← Don't use JSON
"1"                  ← Don't use quotes
[1]                  ← Don't use arrays
```

---

## 🔧 Troubleshooting

### Common Issues:

#### 1. **Validation Error: Input should be a valid string**
- **Problem**: Using JSON format instead of plain text
- **Solution**: Enter just the value, not JSON

#### 2. **Server Connection Failed**
- **Problem**: Server not running or wrong port
- **Solution**: Check terminal for errors, restart server

#### 3. **Import Errors**
- **Problem**: Missing dependencies or wrong Python path
- **Solution**: Activate virtual environment: `myenv\Scripts\activate`

#### 4. **Tool Not Found**
- **Problem**: Server not properly connected
- **Solution**: Disconnect and reconnect in MCP Inspector

### Debug Steps:
1. **Check server logs** in terminal
2. **Verify connection** in MCP Inspector
3. **Test with simple inputs** first
4. **Use Development Server** for easier debugging

---

## 🎯 Quick Start Workflow

1. **Start with Development Server**: `python run_dev_server.py`
2. **Connect in MCP Inspector**: Use `run_dev_server.py` as argument
3. **Test basic tools**: Try `test_nocodb` first
4. **Process keywords**: Use seed keyword tools
5. **Expand keywords**: Use expansion tools
6. **Build clusters**: Group related keywords
7. **Generate content**: Create briefs for clusters

---

## 💡 Pro Tips

- **Start simple**: Use Development Server first
- **Test incrementally**: One tool at a time
- **Check logs**: Terminal shows helpful debug info
- **Use mock data**: Perfect for learning and testing
- **Scale gradually**: Add real APIs when ready

---

## 🌟 Real-World Example Workflow

### Scenario: You want to create content about "SEO Tools"

#### Step 1: Process Seed Keyword
```
Server: Seed Keyword Server
Tool: process_seed_keyword
Input: 1
Result: "seo tools" → normalized and classified as "informational"
```

#### Step 2: Expand Keywords
```
Server: Keyword Expansion Server
Tool: expand_keyword
Input: 1
Result: 25 related keywords like "best seo tools", "free seo tools", etc.
```

#### Step 3: Classify Intent
```
Server: Intent Classifier Server
Tool: classify_intent
Input: 1
Result: Mixed intents - some informational, some commercial
```

#### Step 4: Build Clusters
```
Server: Cluster Builder Server
Tool: build_clusters
Input: (empty)
Result: 3 clusters - "SEO Tools Overview", "Best SEO Tools", "Free SEO Tools"
```

#### Step 5: Generate Content Brief
```
Server: Content Brief Server
Tool: generate_brief
Input: 1 (cluster ID)
Result: Detailed content brief with outline, target keywords, competitor analysis
```

#### Step 6: Analyze Competitors
```
Server: Competitor Snapshot Server
Tool: analyze_competitors
Input: seo tools
Result: Top 10 competitors, their content strategies, gaps to exploit
```

#### Step 7: Track Performance
```
Server: Performance Tracker Server
Tool: track_rankings
Input: (empty)
Result: Current rankings for all your target keywords
```

---

## 🎨 Understanding the Output

### What Each Field Means:

#### Keyword Data:
- **`search_volume`**: How many people search this monthly
- **`keyword_difficulty`**: How hard to rank (0-100, lower = easier)
- **`cpc`**: Cost per click in ads (indicates commercial value)
- **`intent`**: Why people search (info/commercial/transactional)

#### Cluster Data:
- **`pillar_keyword`**: Main keyword for the cluster
- **`total_search_volume`**: Combined volume of all keywords
- **`avg_keyword_difficulty`**: Average difficulty to rank
- **`keyword_count`**: How many keywords in cluster

#### Content Brief Data:
- **`target_keywords`**: Primary and secondary keywords to target
- **`content_outline`**: Suggested structure and sections
- **`competitor_gaps`**: What competitors are missing
- **`recommended_length`**: Optimal word count

---

## 🔄 Server Communication Flow

```mermaid
graph TD
    A[Seed Keywords] --> B[Process & Normalize]
    B --> C[Expand Keywords]
    C --> D[Classify Intent]
    D --> E[Build Clusters]
    E --> F[Generate Briefs]
    F --> G[Analyze Competitors]
    G --> H[Track Performance]

    B --> I[Seed Keyword Server]
    C --> J[Keyword Expansion Server]
    D --> K[Intent Classifier Server]
    E --> L[Cluster Builder Server]
    F --> M[Content Brief Server]
    G --> N[Competitor Snapshot Server]
    H --> O[Performance Tracker Server]
```

---

## 🚨 Important Notes

### Mock Data vs Real Data:
- **Current**: All servers use mock data for demonstration
- **Production**: Replace with real APIs (DataForSEO, OpenAI, etc.)
- **Benefits**: Learn the workflow without API costs

### Security:
- **Local Only**: Servers run on your machine
- **No External Calls**: Mock data means no API requests
- **Safe Testing**: Experiment without affecting real data

### Scalability:
- **Start Small**: Test with mock data
- **Add APIs Gradually**: One service at a time
- **Monitor Costs**: Real APIs have usage limits

---

*This comprehensive guide covers everything you need to know about the MCP server ecosystem for SEO keyword research and content planning. Start with the Development Server and work your way through each tool to understand the complete workflow.*
