# utils/validators.py
from typing import Dict, List, Optional
import re

def validate_keyword(keyword: str) -> bool:
    """Validate a keyword string"""
    if not keyword or not isinstance(keyword, str):
        return False
    
    # Check length
    if len(keyword) < 2 or len(keyword) > 100:
        return False
    
    # Check for invalid characters
    if re.search(r'[<>\"\'\\]', keyword):
        return False
    
    return True

def validate_url(url: str) -> bool:
    """Validate a URL"""
    url_pattern = re.compile(
        r'^https?://'  # http:// or https://
        r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
        r'localhost|'  # localhost...
        r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
        r'(?::\d+)?'  # optional port
        r'(?:/?|[/?]\S+)$', re.IGNORECASE)
    
    return bool(url_pattern.match(url))

def validate_metrics(metrics: Dict) -> bool:
    """Validate SEO metrics"""
    required_fields = ['search_volume', 'keyword_difficulty']
    
    for field in required_fields:
        if field not in metrics:
            return False
        
        value = metrics[field]
        if not isinstance(value, (int, float)) or value < 0:
            return False
    
    # Validate ranges
    if metrics.get('keyword_difficulty', 0) > 100:
        return False
    
    return True

