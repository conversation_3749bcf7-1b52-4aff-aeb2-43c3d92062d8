# run_intent_classifier_server.py
import sys
import os

# Add the project root to Python path FIRST
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# Import and run the intent classifier server
from mcp_servers.intent_classifier_server import mcp

if __name__ == "__main__":
    print("Starting Intent Classifier Server...")
    print("Use MCP Inspector to connect and test")
    mcp.run()
