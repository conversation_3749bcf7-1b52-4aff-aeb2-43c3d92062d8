[project]
name = "mcp-seo-pipeline"
version = "1.0.0"
description = "MCP-based SEO Pipeline with NocoDB and DataForSEO"
dependencies = [
    "mcp[cli]>=1.0.0",
    "httpx>=0.25.0",
    "pydantic>=2.0.0",
    "python-dotenv>=1.0.0",
    "openai>=1.0.0",
    "aiohttp>=3.9.0",
    "aiosqlite>=0.19.0",
    "pandas>=2.0.0",
    "numpy>=1.24.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "ruff>=0.1.0",
]