#!/usr/bin/env python
"""
Test script to demonstrate the MCP SEO Pipeline functionality
"""
import asyncio
import sys
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

async def test_pipeline():
    """Test the SEO pipeline components"""
    print("🚀 MCP SEO Pipeline - Test Run")
    print("=" * 50)
    
    # Test 1: Configuration
    print("\n1. Testing Configuration...")
    try:
        from config.settings import settings
        print(f"✅ Configuration loaded successfully")
        print(f"   - NocoDB URL: {settings.nocodb_api_url}")
        print(f"   - OpenAI Model: {settings.openai_model}")
        print(f"   - Min Search Volume: {settings.min_search_volume}")
    except Exception as e:
        print(f"❌ Configuration error: {e}")
    
    # Test 2: Models
    print("\n2. Testing Data Models...")
    try:
        from models.keyword import Keyword
        from models.cluster import Cluster
        from models.brief import Brief
        
        # Create test keyword
        test_keyword = Keyword(
            raw_keyword="seo tools",
            normalized_keyword="seo tools",
            keyword="seo tools",
            search_volume=1000,
            keyword_difficulty=45.5,
            intent="commercial"
        )
        print(f"✅ Keyword model: {test_keyword.keyword} (Volume: {test_keyword.search_volume})")
        
        # Create test cluster
        test_cluster = Cluster(
            pillar_keyword="seo tools",
            subtopics=["seo tools", "best seo tools", "free seo tools"],
            total_search_volume=5000
        )
        print(f"✅ Cluster model: {test_cluster.pillar_keyword} ({len(test_cluster.subtopics)} keywords)")
        
        # Create test brief
        test_brief = Brief(
            cluster_id=1,
            title_options=["Complete Guide to SEO Tools", "Best SEO Tools 2024"],
            meta_description="Discover the best SEO tools to improve your website rankings and drive more traffic.",
            outline=[{"h2": "Introduction", "h3s": ["What are SEO tools", "Why they matter"]}],
            word_count_target=2500,
            internal_links=["seo-strategy", "keyword-research"],
            faqs=[{"question": "What are the best SEO tools?", "answer": "The best tools include..."}],
            cta_suggestions=["Start your free trial", "Download our SEO guide"]
        )
        print(f"✅ Brief model: {test_brief.title_options[0]} ({test_brief.word_count_target} words)")
        
    except Exception as e:
        print(f"❌ Models error: {e}")
    
    # Test 3: Services (Mock test - no real API calls)
    print("\n3. Testing Service Connections...")
    try:
        from services.nocodb_service import NocoDBService
        from services.dataforseo_service import DataForSEOService
        from services.openai_service import OpenAIService
        
        print("✅ NocoDB Service imported successfully")
        print("✅ DataForSEO Service imported successfully") 
        print("✅ OpenAI Service imported successfully")
        print("   Note: Actual API connections require valid credentials in .env")
        
    except Exception as e:
        print(f"❌ Services error: {e}")
    
    # Test 4: MCP Servers
    print("\n4. Testing MCP Server Imports...")
    server_files = [
        "mcp_servers.seed_keyword_server",
        "mcp_servers.keyword_expansion_server",
        "mcp_servers.intent_classifier_server",
        "mcp_servers.cluster_builder_server",
        "mcp_servers.content_brief_server"
    ]
    
    for server in server_files:
        try:
            # Just test if we can import the module
            __import__(server)
            server_name = server.split('.')[-1].replace('_', ' ').title()
            print(f"✅ {server_name} imported successfully")
        except Exception as e:
            print(f"❌ {server}: {e}")
    
    # Test 5: Pipeline Workflow Simulation
    print("\n5. Simulating Pipeline Workflow...")
    print("   📝 Step 1: Seed Keywords → Normalize & Classify")
    print("   🔍 Step 2: Keyword Expansion → Get metrics from DataForSEO")
    print("   🎯 Step 3: Intent Classification → Categorize by search intent")
    print("   📊 Step 4: Cluster Building → Group related keywords")
    print("   📄 Step 5: Content Briefs → Generate AI-powered outlines")
    print("   🏆 Step 6: Competitor Analysis → Analyze top content")
    print("   📈 Step 7: Performance Tracking → Monitor rankings")
    
    print("\n" + "=" * 50)
    print("🎉 Pipeline Test Complete!")
    print("\nNext Steps:")
    print("1. Set up NocoDB database tables")
    print("2. Add real API credentials to .env file")
    print("3. Use the installed MCP servers in Claude Desktop")
    print("4. Start with seed keywords and run the full pipeline")

if __name__ == "__main__":
    asyncio.run(test_pipeline())
